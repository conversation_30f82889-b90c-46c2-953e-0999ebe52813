# **تحليل نظام إدارة المهام والحياة الشخصية**

## 1. الملخص التنفيذي

يقدم هذا التقرير تحليلًا شاملاً لمشروع برنامج **إدارة المهام والحياة الشخصية** المكتوب بلغة C# باستخدام منصة واجهات WPF وقاعدة بيانات SQLite. يهدف النظام إلى توفير أداة متكاملة لمساعدة المستخدم على تنظيم مهامه اليومية وتتبع عاداتّه وخططه التعليمية وأحداث تقويمه الشخصي، كل ذلك عبر واجهة مستخدم حديثة وسهلة الاستخدام. يتيح التطبيق للمستخدم تسجيل الدخول بكلمة مرور رئيسية، مع إمكانية وجود كلمات مرور خفية لتفعيل ميزات سرية إضافية. يتضمن البرنامج أربعة أقسام رئيسية مترابطة: قسم تتبع **العادات اليومية**، قسم إدارة **الكورسات التعليمية**، قسم **التقويم** لجدولة المهام والأحداث، وقسم **الإحصائيات** الذي يولّد تقارير مرئية عن أداء المستخدم. كما يحتوي على **إعدادات تخصيص** شاملة (مثل السمات والألوان) لتحسين تجربة المستخدم. يهدف المشروع إلى رفع إنتاجية الأفراد وتحسين عاداتهم اليومية عبر أداة تقنية آمنة وفعّالة. سيتم في هذا التقرير تناول تفاصيل المتطلبات والواجهة والتصميم المعماري وقاعدة البيانات والجوانب الأمنية وتجربة المستخدم، بالإضافة إلى خطة تنفيذ المشروع والمخاطر المتوقعة وسبل التعامل معها، مع مقارنة موجزة بحلول بديلة موجودة في السوق. النتيجة المتوقعة هي تطبيق مكتبي احترافي يسهم في تحسين إنتاجية المستخدم وجودة حياته الشخصية بتنظيم أعماله وعاداته بشكل فعّال.
## 2. دراسة المشكلة والحاجة

يعاني كثير من الأفراد من تشتت أدوات التنظيم الشخصي؛ فقد يستخدم الواحد منهم تطبيقًا لتتبع المهام، وآخر لتتبع العادات، وثالثًا لمتابعة الدورات التعليمية، مما يصعّب الحفاظ على رؤية شاملة لجميع جوانب حياته اليومية. **المشكلة** التي يستهدفها هذا المشروع هي الافتقار إلى منصة موحّدة تجمع بين إدارة المهام والعادات والتعلم والتقويم الشخصي في آن واحد. غياب مثل هذه الأداة المتكاملة قد يؤدي إلى ضياع المواعيد، أو نسيان بعض الأنشطة الهامة، أو فقدان التحفيز للاستمرار في **تطوير العادات الشخصية**.

تأتي **الحاجة** لبرنامج شامل من أهمية تتبع العادات وتنظيم المهام بالنسبة للإنتاجية والصحة النفسية. تظهر الدراسات أن بناء العادات أمر صعب ويتطلب أكثر من مجرد النوايا الحسنة؛ وهنا تبرز فائدة أدوات تتبع العادات والمهام في **تحسين الالتزام وتحفيز المستخدم**. فتتبع العادات يساعد الفرد على **البقاء مسؤولًا** أمام نفسه، ويُمكّنه من **تحديد الأنماط السلوكية** في حياته، كما يعطيه **جرعة تحفيزية (دوبامين)** عند إنجاز المهام والعادات على سبيل المثال، توفر هذه الأدوات وسيلة مرئية لقياس التقدّم وتحديد النكسات المتكررة، مما يساعد المستخدم على تعديل خططه وتحسين استراتيجياته في إدارة وقته وعاداته. وبالإضافة إلى ذلك، يعاني الكثيرون من **فجوة بين النية والتنفيذ** فيما يتعلق بتبنّي عادات جديدة أو الالتزام بخطط طويلة الأمد، لذا فإن وجود تطبيق يذكّر المستخدم بأهدافه يوميًا ويوثق إنجازاته يمكن أن يسد هذه الفجوة.

بشكل عام، **أهمية هذا البرنامج** تكمن في توفير _مساعد رقمي شخصي_ للمستخدم لتنظيم حياته: فهو يحل مشكلة تشتت المعلومات بين عدة أدوات منفصلة عبر جمعها في نظام واحد متكامل، مما يرفع الكفاءة ويقلل الجهد المهدور في التنقل بين التطبيقات والجداول الورقية. كذلك يلبي الحاجة لمتابعة التطوّر الشخصي (مثل تعلم مهارة جديدة عبر الكورسات) جنبًا إلى جنب مع إنجاز المهام وتكوين العادات الإيجابية. ونتيجة لذلك، يتوقع أن يساهم النظام في **زيادة الإنتاجية وتحسين جودة الحياة** للمستخدم عبر التنظيم والاستمرارية في تحقيق الأهداف اليومية.

## 3. الأهداف

يهدف المشروع إلى تحقيق مجموعة من **الأهداف الرئيسية** التي تخدم تحسين إنتاجية المستخدم وتنظيم حياته، ومن أبرز هذه الأهداف:

·       **تحسين الإنتاجية الشخصية**: من خلال جمع المهام اليومية والأسبوعية في مكان واحد منظم، بحيث يتمكن المستخدم من التركيز على الأولويات وتجنب النسيان أو التأجيل.

·       **تتبع العادات وبناء روتين إيجابي**: دعم المستخدم في تبنّي عادات يومية صحية وإيجابية (مثل ممارسة الرياضة أو القراءة) عبر تسجيلها وتتبعها يوميًا، مما يعزز الاستمرارية والمساءلة الذاتية.

·       **إدارة التعلم والتطوير الذاتي**: تمكين المستخدم من متابعة تقدمه في الدورات التعليمية أو الكورسات التي يلتحق بها، عبر تنظيم محتوى التعلم ووضع أهداف مرحلية وإشعارات للتقدم.

·       **تنظيم الجدول الشخصي**: توفير تقويم شامل يجمع بين المواعيد الشخصية ومهام العمل وأحداث الكورسات والعادات المجدولة، ليسهل تخطيط الأيام والأسابيع بشكل متوازن.

·       **توفير نظرة شمولية للأداء**: من خلال قسم الإحصائيات الذي يعرض رسومًا بيانية وجداول توضح مدى التزام المستخدم بمهامه وعاداته، وقياس التطور في فترات زمنية مختلفة (يومي، أسبوعي، شهري).

·       **تعزيز التحفيز والاستمرار**: عبر توظيف تقنيات مثل عرض _سلسلة الإنجازات_ (Streak) في العادات أو _نقاط الإنجاز_ في الكورسات، مما يحفز المستخدم على المواصلة لتحقيق أهدافه دون انقطاع.

·       **تجربة مستخدم مخصصة وممتعة**: بتقديم واجهة جذابة قابلة للتخصيص (الألوان، الوضع الليلي/النهاري، أصوات الإشعارات) لتلائم تفضيلات المستخدم الفردية، مما يجعل استخدام التطبيق يوميًا تجربة سلسة.

·       **حماية خصوصية البيانات الشخصية**: عبر تخزين المعلومات محليًا بشكل آمن (في SQLite) وكلمة مرور لحماية الدخول، بحيث تبقى بيانات المستخدم الحساسة (مثل مذكراته الشخصية ضمن المهام أو تقدمّه في عادات معينة) مصونة.

·       **تطوير المهارات البرمجية والشخصية** (هدف للمطوّر/صاحب المشروع): يهدف المشروع أيضًا إلى تطبيق معايير **تحليل النظم الاحترافية** (SRS) في التنفيذ، مما يعود بالفائدة التعليمية على فريق التطوير نفسه في تحسين مهاراتهم في هندسة البرمجيات وإدارة المشاريع.

هذه الأهداف تترابط فيما بينها لتحقيق الرسالة الأعمّ للمشروع وهي **تمكين المستخدم من إدارة حياته اليومية بكفاءة أعلى وضغط نفسي أقل**، بحيث يشعر بالسيطرة والتنظيم في مختلف جوانب نشاطاته اليومية.

## 4. تحليل المتطلبات

### 4.1 المتطلبات الوظيفية (Functional Requirements)

المتطلبات الوظيفية تصف ما يجب على النظام أن يفعله والميزات والخدمات التي يقدمها للمستخدم. فيما يلي أهم المتطلبات الوظيفية لنظام إدارة المهام والحياة الشخصية:

·       **تسجيل الدخول والصلاحيات**: يجب أن يدعم النظام شاشة تسجيل دخول بكلمة مرور أساسية موحّدة لدخول التطبيق. عند إدخال كلمة المرور الصحيحة يتم فتح الواجهة الرئيسية. كما يجب أن يتعرف النظام على _كلمات مرور خفية خاصة_ تؤدي إلى تفعيل أو فتح ميزات مخفية إضافية (مثل وضع المطوّر أو إحصاءات متقدمة)، وذلك حسب ما يتم تعريفه مسبقًا في التطبيق.

·       **إدارة العادات (Habits Management)**: يتيح النظام للمستخدم إضافة عادات جديدة مع تحديد اسم العادة ووصفها وتكرارها (يومي، أسبوعي... إلخ). يمكن للمستخدم تأشير إنجاز العادة لكل يوم (مثلاً بوضع علامة "تم" أو إدخال قيمة مثل عدد صفحات قُرئت). يحتفظ التطبيق بسجل تاريخي لإنجازات كل عادة، ويحسب تلقائيًا **سلسلة الأيام الناجحة** وتنبيهات في حال انقطاع السلسلة. كما يمكن للمستخدم تعديل أو حذف عادات موجودة.

·       **إدارة الكورسات التعليمية (Courses)**: يوفر التطبيق إمكانية إضافة قائمة بالدورات التعليمية أو الكورسات التي يتابعها المستخدم. لكل كورس يمكن تخزين معلومات مثل اسم الدورة، الجهة المقدمة، المدة أو عدد الدروس، نسبة الإنجاز أو التقدم. يتمكن المستخدم من تحديد مهام أو أهداف ضمن كل دورة (مثلاً إنهاء فصل معين قبل تاريخ محدد) وربطها بالتقويم لتذكيره. يمكن وسم كل كورس بحالته (نشط/مكتمل/مؤجل) وترتيبه حسب الأولوية أو المجال. كما تعرض الواجهة مستوى التقدم (Progress) لكل دورة بصورة واضحة (مثلاً شريط نسبة مئوية).

·       **إدارة المهام والتقويم (Tasks & Calendar)**: يحتوي النظام على تقويم تفاعلي يسمح للمستخدم بإضافة **مهام وأحداث مجدولة** مع تواريخ وأوقات محددة. يمكن عرض التقويم بأسلوب شهري أو أسبوعي، مع تمييز الأيام التي تحتوي على مهام أو عادات يجب تنفيذها. على سبيل المثال، يمكن جدولة "موعد اجتماع" أو "مذاكرة فصل 2 من دورة Python" أو "تمرين رياضي" في التقويم. يدعم التقويم التنبيهات (Notifications) لإشعار المستخدم باقتراب المواعيد المهمة. كما يمكن للمستخدم تعديل أو إلغاء المهام من التقويم بسهولة عبر السحب والإفلات أو قوائم الخيارات.

·       **قسم الإحصائيات والتقارير (Statistics & Reports)**: يولّد النظام لوحات بيانات ورسوم بيانية تعكس نشاط المستخدم. من أمثلة التقارير: رسم بياني يوضح عدد المهام المنجزة يوميًا خلال الأسبوع، مخطط يبيّن **نسبة الالتزام بالعادة X** خلال الشهر الماضي، أو مخطط دائري يوزع وقت المستخدم على فئات (عمل، دراسة، عادات صحية). هذه الإحصائيات يتم احتسابها بناءً على البيانات المدخلة في الأقسام الأخرى (المهام، العادات، الكورسات) وتُعرض بشكل تفاعلي مرئي. يمكن للمستخدم اختيار نطاقات زمنية مختلفة، كما يمكنه استخراج تقرير ملخص (مثلاً ملف PDF) لتتبع أدائه الشخصي.

·       **ميزة البحث والتصفية**: يسمح النظام للمستخدم بالبحث عن عنصر معين (مهمة، عادة، كورس) عبر شريط بحث موحّد. كما يمكن تطبيق **مرشحات (Filters)**، مثل عرض المهام ذات الأولوية العالية فقط، أو العادات التي تم إنجازها على مدار 7 أيام متتالية، أو الكورسات في فئة معينة. هدف هذه الميزة تسهيل الوصول للمعلومات عند كثرة البيانات في التطبيق.

·       **إعدادات التخصيص (Customization Settings)**: يتيح التطبيق للمستخدم تخصيص مظهره وسلوكه عبر صفحة إعدادات. تشمل الخيارات: اختيار **ثيم** (سمة) ملون أو وضع داكن/فاتح، تعديل حجم الخط ونوعه لضمان راحة القراءة، تشغيل أو إيقاف الأصوات المصاحبة للتنبيهات، إعداد أوقات التذكير اليومية للعادات (مثلاً تذكير كل يوم الساعة 8م بملء مدخلات اليوم). كذلك يمكن للمستخدم ضبط كلمة المرور الرئيسية من خلال الإعدادات (إن أردنا دعم تغيير كلمة المرور الافتراضية بدلاً من بقائها ثابتة).

·       **المزامنة والنسخ الاحتياطي (إن وجد)**: في حال التخطيط مستقبلاً لدعم أجهزة متعددة أو حفظ سحابي، يمكن وضع متطلب لدعم مزامنة البيانات عبر الأجهزة أو على الأقل **نسخ احتياطي محلي** لقاعدة البيانات (مثل توليد ملف backup يصدره المستخدم ويحفظه).

·       **التفاعل بين الأقسام**: يجب أن تكون الأقسام مترابطة بسلاسة. مثلاً، قد يرتبط حدث في التقويم بتحقيق عادة معينة، أو يمكن إضافة مهمة مرتبطة بكورس تعليمي مباشرة من قسم الكورسات لجدولتها في التقويم. هذه التكاملات تسهل على المستخدم إدخال البيانات مرة واحدة والاستفادة منها في عدة نواحٍ.

### 4.2 المتطلبات غير الوظيفية (Non-Functional Requirements)

المتطلبات غير الوظيفية تصف معايير الجودة والقيود التي يجب أن يعمل ضمنها النظام، وتشمل الأداء والأمان وقابلية الاستخدام وغيرها. فيما يلي أبرز المتطلبات غير الوظيفية:

·       **قابلية الاستخدام (Usability)**: يجب أن تكون واجهات التطبيق بديهية وسهلة التنقل، مع منحنى تعلم منخفض للمستخدم الجديد. يجب استخدام **تسميات واضحة** وأيقونات معبّرة لكل قسم (مثلاً أيقونة قائمة مرجعية للمهام، ساعة رملية أو قلب للعادات، كتاب للكورسات، تقويم للتقويم). كما ينبغي توفير إرشادات/تلميحات (Tooltips) عند تمرير المؤشر على الأزرار لشرح وظيفتها. التجربة الكلية يجب أن تكون مريحة وباللغة المحلية للمستخدم (مثال: الواجهة هذه ستكون باللغة العربية بالكامل).

·       **الأداء والكفاءة (Performance)**: ينبغي أن يعمل التطبيق بسرعة وسلاسة على أجهزة الكمبيوتر الشخصية ذات المواصفات المتوسطة. يجب ألا تتجاوز **زمن الاستجابة** عند التنقل بين النوافذ أو تحميل التقويم بضع ثوانٍ. استخدام قاعدة بيانات SQLite محليًا يعني أن عمليات الاستعلام عن المهام والعادات يجب أن تتم في جزء من الثانية لمعظم العمليات. أيضًا يجب أن يكون استهلاك التطبيق للذاكرة والمعالج معقولاً (خاصة عند عرض رسوم بيانية في قسم الإحصائيات).

·       **الأمان (Security)**: يتوجب حماية **وصول المستخدم** عبر كلمة المرور. يجب تخزين كلمة المرور أو التحقق منها بطريقة آمنة (مثل تخزينها مجزأة **Hash** بدل النص الصريح). كذلك، الكلمات السرية الخاصة يجب ألا تكون مكشوفة في منطق التطبيق بشكل يسهل اكتشافه (يمكن مثلاً ترميزها أو تخزينها في شكل مشفّر داخل التطبيق). أما بيانات المستخدم المخزنة في SQLite فيُفضل **تشفير قاعدة البيانات** أو على الأقل حماية ملفها بمنع الوصول لغير المستخدم صاحب الجهاز. الجانب الأمني يشمل أيضًا مقاومة محاولات تجاوز شاشة الدخول (مثال: تعطيل التطبيق بعد عدد محدد من محاولات الدخول الخاطئة كإجراء إضافي).

·       **موثوقية واعتمادية (Reliability)**: يجب أن يكون النظام مستقرًا ويعمل دون أعطال مفاجئة. يتطلب ذلك اختبارًا شاملًا لكل وحدة (العادات، الكورسات، ...إلخ) لضمان عدم حدوث انهيار عند إدخال بيانات غير متوقعة. كما ينبغي توفير آلية **حفظ تلقائي** (Auto-save) أو تجنّب خسارة البيانات عند إغلاق التطبيق بشكل غير متوقع.

·       **قابلية الصيانة (Maintainability)**: نظرًا لاستخدام بنية MVVM (نمط Model-View-ViewModel) في التطبيق، فإن فصل منطق العمل عن الواجهة يعني قابلية أفضل للصيانة والتطوير المستقبلي يجب أن يُكتب الكود بطريقة منظمة مع توثيق داخلي وتعليقات توضح عمل الوظائف الأساسية، بحيث يسهل على مطوّر آخر فهم النظام وإجراء التحسينات أو إصلاح الأخطاء عند الحاجة.

·       **المرونة وقابلية التطوير (Scalability & Extensibility)**: على الرغم من أن التطبيق مخصص لمستخدم واحد على جهاز مكتبي حاليًا، ينبغي تصميمه بحيث يمكن **توسيعه** مستقبلًا لإضافة مزايا مثل **مزامنة سحابية** أو نسخة للهواتف الذكية. هذا يعني هيكلة الأكواد والاعتماديات بحيث يمكن استبدال طبقة البيانات (SQLite) بأخرى (خادم قواعد بيانات مركزي) أو إضافة واجهات برمجية (APIs) للتواصل مع تطبيق جوال.

·       **التوافقية (Compatibility)**: يجب أن يكون التطبيق متوافقًا مع أنظمة تشغيل ويندوز الحديثة (Windows 10/11 مثلًا) حيث تعمل تطبيقات WPF. كما يجب تضمين جميع المكتبات المطلوبة في حزمة التثبيت لضمان عمله على أجهزة المستخدمين دون الحاجة لتثبيتات إضافية معقدة.

·       **الواجهات البينية (Interoperability)**: إن رغبنا مستقبلاً في التكامل مع خدمات أخرى (مثل تقويم Google أو تطبيقات تتبع الوقت)، فيجب أخذ ذلك بالحسبان من البداية بتصميم هيكلية بيانات **قياسية** وإتاحة إمكانية التصدير والاستيراد (Export/Import) بصيغ شائعة مثل CSV أو JSON لبيانات المهام والعادات.

·       **سهولة الاستخدام لمتخذي القرار (User Feedback)**: يجب أن يوفر النظام تغذية راجعة ملائمة للمستخدم عند قيامه بالعمليات (مثلاً رسالة تأكيد عند إضافة عادة بنجاح، تنبيه عند محاولة حذف كورس). هذا الجانب غير الوظيفي يعزّز الثقة في النظام ويقلل الأخطاء عبر توجيه المستخدم.

بتلبية هذه المتطلبات غير الوظيفية، سنضمن أن التطبيق لا يحقق المطلوب منه وظيفيًا فحسب، بل يقدّم أيضًا **جودة عالية** في الأداء والأمان وقابلية الاستخدام على المدى الطويل.

## 5. تحليل المستخدم المستهدف (User Personas)

يستهدف هذا التطبيق شريحة واسعة من المستخدمين الذين يطمحون إلى تحسين تنظيم حياتهم وزيادة إنتاجيتهم. فيما يلي **شخصيتان تخيّليتان (Personas)** تمثلان الفئات المستهدفة، مع توضيح احتياجات كل منهما وكيف سيفيدها التطبيق:

- **أحمد – الموظف الطموح (30 سنة)**: يعمل أحمد كمهندس برمجيات، ولديه العديد من المهام المهنية اليومية بالإضافة إلى رغبته في تطوير نفسه عبر دورات تدريبية على الإنترنت. يواجه أحمد تحديًا في الموازنة بين عمله بدوام كامل وتعلم التقنيات الجديدة وممارسة الرياضة اليومية. **احتياجاته**: أداة تجمع بين جدول اجتماعات العمل (الذي قد يدوّنه حاليًا في مفكرة أو Outlook) وخطة تعلمه (دورات عبر الإنترنت تحتاج متابعة) وجدول تمارينه الرياضية. الحل بالنسبة له هو تطبيقنا الذي سيمكنه من: وضع مهامه العملية والشخصية في **تقويم واحد** متكامل، وتتبع عادات مثل "التمرين اليومي" أو "القراءة 30 دقيقة" يوميًا، ومراقبة تقدمه في كورس مثلاً "إدارة المشاريع PMP" بنسبة مئوية. سيستفيد أحمد من **التنبيهات الذكية** لتذكيره بأهم أولوياته كل يوم، ومن لوحات الإحصائيات لرؤية إنجازه الأسبوعي مما يحفزه على الاستمرار. كذلك سيقدّر خاصية **الوضع الليلي** نظرًا لساعات عمله الطويلة ليلًا.
- **سارة – طالبة جامعية منظمة (22 سنة)**: سارة طالبة في السنة الأخيرة جامعية تحضّر مشروع تخرج، وفي نفس الوقت تحاول بناء عادات إيجابية مثل الاستيقاظ مبكرًا وممارسة التأمل وتعلّم لغة جديدة. هي من النوع الذي يحب التخطيط وتدوين المهام ولكنها تجد صعوبة في تتبع كل شيء عندما تتعدد الأدوات (تستخدم حالياً تطبيق للملاحظات لمهام الدراسة، وتطبيق مختلف للعادات مثل شرب الماء، وتقويم ورقي للفعاليات). **احتياجاتها**: منصة واحدة تستطيع من خلالها إدخال _جدول محاضراتها ومواعيد تسليم الواجبات_، وفي نفس الوقت تسجيل **عاداتها اليومية** وإكمالها بسهولة دون نسيان. تطبيقنا يمنحها **واجهة بصريّة جميلة** يمكن أن تضيف إليها ملصقات أو ألوان تفضّلها (مثلاً تلوّن العادات الصحية بالأخضر). ستحب سارة ميزة **تقارير الإحصاء** التي تظهر لها معدل التزامها بالأهداف (مثلاً الرسم البياني الأسبوعي للعادات)، مما يساعدها على تحديد أوقات الضغط الدراسي التي تحتاج فيها لتخفيف الأهداف الجانبية. كما أنها ستستفيد من وجود **كلمة مرور** للتطبيق لتحفظ خصوصية خططها الشخصية بعيدًا عن أعين الآخرين.
- **(شخصية إضافية محتملة)** _خالد – مطوّر مستقل (Freelancer) وأب لطفلين_: خالد يعمل بشكل مستقل من المنزل، ما يعني أنه مسؤول عن تنظيم وقته بين المشاريع لعملائه والعناية بأسرته وتطوير مهاراته. يحتاج خالد إلى تطبيق يساعده في **تجزئة وقته** بين العمل (تسليم مشاريع بمهل زمنية محددة)، وعائلته (مواعيد أولاده المدرسية وحضور المناسبات)، وتنمية نفسه (كورسات برمجية، تمارين رياضية). إن استخدامه لتطبيقنا سيمكّنه من مزامنة كل هذه الخيوط في تقويم واحد ورؤية شاملة، مع إمكانية تعيين **تنبيهات متكررة** لبعض العادات العائلية (مثل "خصص ساعة للعب مع أطفالك")، وهذا جانب مهم لأنه تطبيق ليس للعمل فقط بل لتحقيق توازن حياتي. سيقدّر خالد أيضًا خاصية **الكلمات السرية المخفية** إن وُجدت، لأنها قد تتيح ميزات متقدمة مثل "وضع التركيز" الذي يخفي المهام غير المهمة مؤقتًا أو "وضع الطوارئ" الذي يظهر له المهام الحرجة فقط حين يضيق وقته.

هذه الشخصيات التمثيلية توضح أن المستخدم المستهدف للتطبيق هو **كل شخص لديه مزيج من المسؤوليات والرغبة بالتطوير الشخصي**، سواء كان طالبًا أم موظفًا أو مستقلًا. يجمعهم هدف مشترك هو تحسين إدارة الوقت وبناء العادات، والتطبيق مصمم بمرونة ليناسب احتياجاتهم المتنوعة (من تخطيط المشاريع الكبيرة إلى تتبع أبسط العادات اليومية).

## 6. الوصف المعماري للنظام

تم بناء هذا النظام باستخدام **لغة #C وإطار عمل WPF** (Windows Presentation Foundation) لواجهة المستخدم، مع الاعتماد على **قاعدة بيانات SQLite** محليّة لتخزين البيانات. يعتمد التصميم المعماري على نمط **MVVM (Model-View-ViewModel)** وهو نمط شائع في تطبيقات WPF يساعد على فصل منطق العمل عن واجهة المستخدم لضمان قابلية الصيانة والاختبار. يوضح الشكل التالي البنية العامة للنظام على شكل مخطط يبرز الطبقات الرئيسية وعلاقاتها:

**الشكل: هيكلية MVVM عامة في تطبيق WPF** – في هذه البنية، الـ_View_ (الواجهة) تتواصل مع الـ_ViewModel_ عبر ربط البيانات (Data Binding) والأوامر، والـ_ViewModel_ بدوره يتعامل مع بيانات الـ_Model_ (قاعدة البيانات والكائنات الممثلة للمعلومات). هذا الفصل يضمن تصميمًا **مرنًا وذو ترابط ضعيف** بين المكونات، حيث لا يعرف الـView تفاصيل الـModel، ولا يحتاج الـViewModel إلى مرجع مباشر للـView.

ضمن هذا الإطار العام، يتألف التطبيق من المكونات التالية: - **طبقة العرض (Presentation Layer)**: مكونة من نوافذ WPF وعناصر تحكم XAML تمثل واجهة المستخدم (مثل نافذة تسجيل الدخول، نافذة رئيسية تحتوي على تبويبات العادات والكورسات والتقويم والإحصائيات). كل نافذة لها _ViewModel_ خاص بها يقوم بتوفير البيانات للعرض والاستجابة لتفاعلات المستخدم. على سبيل المثال، يوجد HabitsViewModel لتوفير قائمة العادات وربطها بعناصر واجهة عرض العادات، و CalendarViewModel لإدارة الأحداث في التقويم. - **طبقة المنطق (ViewModel & Business Logic)**: في هذه الطبقة تتم معالجة المدخلات وتطبيق قواعد العمل. الـViewModels تستقبل الأوامر من الواجهات (مثل أمر AddHabitCommand عند ضغط زر إضافة عادة) فتقوم باستدعاء خدمات أو وظائف مناسبة في الطبقة الأدنى (Model) ثم تحدّث الخصائص المنعكسة على الواجهة. كما تحتوي الطبقة على منطق تحقيق الميزات الخاصة مثل التحقق من كلمة المرور عند تسجيل الدخول، وآلية التعامل مع _الكلمات السرية المخفية_ (مثلاً SecretCommandService الذي يفحص كلمة المرور المدخلة ويحدد إن كانت تفعيلًا لوظيفة خاصة). - **طبقة البيانات (Model & Data Access)**: تشمل تعريف الكيانات الأساسية (كائنات classes مثل Habit, Course, CalendarEvent التي تمثل جداول قاعدة البيانات) إضافة إلى طبقة الوصول لقاعدة البيانات. في حالتنا هذه، استخدام SQLite يتم إما عبر مكتبة خفيفة (ADO.NET مع SQL queries) أو باستخدام تقنيات ORMs خفيفة مثل Entity Framework Core (إذا كان بسيطًا). على سبيل المثال، قد يكون لدينا DatabaseHelper أو _Repository Classes_ مثل HabitsRepository تحتوي على وظائف لإضافة عادة جديدة أو جلب قائمة العادات من قاعدة البيانات. قاعدة البيانات SQLite نفسها مخزنة في ملف محلي (قد يكون اسمه مثلاً tasks.db).

**العلاقات بين الطبقات**: عندما يقوم المستخدم بعملية (مثلاً إضافة مهمة في التقويم)، يتم تعبئة نموذج المهمة في واجهة التقويم (CalendarView)، يقوم المستخدم بالضغط على "حفظ"، هذا يرتبط بأمر في CalendarViewModel الذي يتولى إنشاء كائن المهمة الجديد وإرساله إلى طبقة البيانات (استدعاء دالة في EventRepository مثلاً لإدخال سجل جديد في جدول الأحداث بقاعدة البيانات). بعد نجاح العملية، يقوم ViewModel بتحديث الخاصية المرتبطة بقائمة الأحداث المعروضة، فترى الواجهة التغيير مباشرة بسبب الربط (binding) وإشعارات تغيير الخاصية (INotifyPropertyChanged). هذه السلسلة تحقق تزامنًا دون الحاجة لأن يعرف الـView تفاصيل التخزين. كما يسهّل هذا النمط **اختبار الوحدات**؛ يمكن اختبار ViewModel بمنأى عن الواجهة عن طريق محاكاة بيانات الـModel.

**مكونات إضافية**: يستعين التطبيق بعدد من المكتبات/الأطر وفق الحاجة: مثلاً _مكتبة رسوم بيانية_ لعرض الإحصائيات (مثل LiveCharts أو OxyPlot)، أو مكتبة _إدارة الثيمات_ (مثل MahApps.Metro أو ModernWpf) لإتاحة تخصيص المظهر بسهولة. هذه الإضافات تندمج في الطبقة المناسبة (الرسوم البيانية ضمن View وViewModel للإحصائيات، أما نظام الثيمات في طبقة العرض مع موارد ResourceDictionary).

باختصار، المعمارية تعتمد **تقسيم المسؤوليات**: واجهة لغرض العرض والتفاعل، طبقة ViewModel للتحكم والتنسيق، وطبقة Model/Database للتخزين. هذا الأسلوب يضمن مرونة تطبيق التغييرات المستقبلية (كتبديل واجهة WPF بتطبيق جوال مثلاً مع الاحتفاظ بنفس منطق ViewModels) ويساعد في المحافظة على **تنظيم الكود واستقراره** مع نمو المشروع.

## 7. تصميم واجهات المستخدم (UI Design)

تم تصميم واجهات المستخدم في هذا المشروع لتكون **جذابة وبسيطة** في آن واحد، مع الالتزام بالمعايير الحديثة لتجربة المستخدم. تعتمد الواجهة على **تقسيم واضح للأقسام** عبر قائمة تنقل رئيسية (قد تكون عبارة عن تبويبات علوية أو قائمة جانبية) تُمكّن المستخدم من الانتقال بين: العادات، الكورسات، التقويم، الإحصائيات، والإعدادات.

### الهوية البصرية والألوان:

اختيرت مجموعة ألوان هادئة ومتناسقة تعزز قابلية القراءة وتمنح شعورًا بالتركيز. مثلاً يمكن استخدام لون أزرق معتدل للعناصر التفاعلية (الأزرار والتبويبات النشطة)، مع خلفية فاتحة محايدة للنوافذ. يدعم التصميم **وضع داكن** وشاحب ليتناسب مع تفضيلات المستخدم المختلفة وظروف الإضاءة. الخطوط المستخدمة هي خطوط واضحة للغة العربية (مثل خط _Segoe UI_ أو _Helvetica Arabic_) بأحجام مناسبة (حوالي 12-14 نقطة للنص الأساسي) لتجنب إجهاد العين.

### تخطيط الشاشات:

·       **الشاشة الرئيسية**: بعد تسجيل الدخول الناجح، يُعرض لوحة تحكم أو صفحة رئيسية تعرض **نظرة سريعة (Dashboard)** تتضمن أهم المعلومات لليوم الحالي: عدد المهام المقررة اليوم، العادات التي يجب القيام بها (مع مؤشر إن كانت اكتملت أم لا)، وربما لمحة عن الفعاليات القادمة. هذه الصفحة تساعد المستخدم على فهم التزاماته فور فتح التطبيق.

·       **شاشة العادات**: مصممة كقائمة أو شبكة من البطاقات، كل بطاقة تمثل عادة واحدة. تظهر البطاقة اسم العادة وربما أيقونة تمثل فئتها (مثلاً أيقونة كتاب لعادة قراءة، دمبل لعادة رياضة). يتم وضع **زر تأشير** (Checkbox أو زر "تم اليوم") بجانب كل عادة ليقوم المستخدم بتحديد إنجازه لليوم مباشرة. كما يمكن الدخول لتفاصيل عادة ما (بنقرها) لفتح نافذة تفصيلية تعرض إحصائياتها كسلسلة الأيام التي أُنجزت فيها وخيارات لتحرير إعدادات العادة (مثل تغيير الهدف اليومي أو وقت التذكير).

·       **شاشة الكورسات**: عبارة عن جدول أو قائمة بالكورسات المسجلة. كل صف/عنصر يعرض اسم الكورس وتقدم المستخدم فيه (مثلاً شريط تقدم progress bar أو نسبة مئوية)، وربما الموعد القادم المتعلق به (مثل "امتحان في 15 مارس" أو "درس جديد غدًا"). يتيح القسم زر "إضافة كورس" لفتح نموذج إدخال تفاصيل كورس جديد (الاسم، عدد الدروس أو المدة، ...) وكذلك زر لتحديث حالة التقدم (مثلاً وسم درس كمكتمل).

·       **شاشة التقويم**: تعرض **التقويم الشهري** مع تمييز أيام تحتوي أحداثًا. يمكن النقر على أي يوم لعرض قائمة المهام/الأحداث في ذلك اليوم (ربما تظهر في جانب الشاشة أو كنافذة منبثقة). يوفر القسم أزرار تنقل (التالي/السابق للشهر أو الأسبوع) وزر لإضافة مهمة/حدث جديد بتحديد التاريخ. لون تمييز المهام قد يختلف حسب نوعها أو أولويتها؛ على سبيل المثال يمكن تظليل التواريخ التي لدى المستخدم فيها **مهام كثيرة** بلون أكثر بروزًا كتنبيه بصري. شكل التقويم مستوحى من النماذج المألوفة مثل تقويم Google مع تعديلات تناسب لغة العرض (مثلاً بدء الأسبوع بيوم السبت إن كان المستخدم يفضل ذلك).

·       **شاشة الإحصائيات**: عبارة عن مجموعة من علامات التبويب الداخلية أو البطاقات، كل منها تعرض رسمًا بيانيًا أو مؤشرًا معينًا. مثلاً تبويب "إنجاز المهام" فيه مخطط يوضّح المهام المنجزة مقابل تلك المؤجلة خلال فترة محددة، تبويب "العادات" يعرض مخططًا خطيًا لاستمرار سلسلة العادات الناجحة، تبويب "توزيع الوقت" قد يحتوي رسمًا دائريًا يبيّن كيف وُزّع وقت المستخدم بين مجالات مختلفة. تم اختيار **الرسوم البيانية** بألوان متدرجة بشكل يبرز المعلومة الرئيسية (كاللون الأخضر للإنجازات، والأحمر للتأخيرات). يدعم القسم تفاعل المستخدم كتغيير نطاق التاريخ من خلال قوائم منسدلة أو أزرار (أسبوعي، شهري، سنوي).

_شكل توضيحي: مثال لواجهة تطبيق تعرض التقويم مع المهام المجدولة وقائمة مهام جانبية._

### العناصر التفاعلية وتجربة الاستخدام:

تم اتباع مبادئ **التصميم المتمحور حول المستخدم**. مثلاً، في جميع الشاشات، الأزرار الأكثر استخدامًا تُوضع في أماكن بارزة (كزر إضافة الذي يظهر دائمًا في أسفل يمين الشاشة بشكل _زر عائم_ في تصميمات حديثة أو أعلى القائمة بوضوح). تم الحرص على **التناسق**: فآلية إضافة عنصر جديد متشابهة عبر الأقسام (نفس رمز الـ"+" ونفس نمط نافذة الحوار)، كذلك ألوان الحالات (الأخضر دائمًا يدل على النجاح أو الاكتمال، الأصفر للتنبيه، الأحمر للتأخير).

التطبيق أيضًا متجاوب مع أحجام النوافذ المختلفة؛ حيث يمكن تكبير النافذة ليظهر التقويم والعادات جنبًا إلى جنب على شاشة كبيرة، أو تصغيره فينhideجزئيات أقل أهمية. في الوضع المصغر، قد يتحول **نص القائمة** إلى أيقونات فقط للحفاظ على المساحة.

### النماذج والحوارات:

لكل عملية تعديل أو إضافة، يظهر **مربع حوار Modal** بسيط. على سبيل المثال، عند الضغط على إضافة مهمة في التقويم، يظهر مربع يحتوي على حقول: عنوان المهمة، الوصف، التاريخ والوقت، أولوية (خيارات)، وربط المهمة بقسم (عادة/كورس) إن وجد. تم تصميم هذه المربعات بحيث تكون **مختصرة وواضحة**، مع استخدام قوائم منسدلة وتحديدات جاهزة لتسهيل الإدخال (مثلاً قائمة لاختيار الكورس المرتبط بدل كتابة الاسم يدويًا). بعد إدخال المعلومات، يمكن للمستخدم الضغط على _"حفظ"_ (زر واضح باللون الأساسي) أو _"إلغاء"_ للعودة.

### قابلية التخصيص:

من ضمن تصميم الواجهة، روعي أن يكون **تغيير الثيم والألوان** سهلًا. يمكن ذلك باستخدام Resource Dictionaries في WPF بحيث يتم تطبيق لون أو خط عام على كافة العناصر. في شاشة الإعدادات، حين يختار المستخدم مثلاً "سمة داكنة"، يجري التطبيق تلقائيًا تغيير موارد الألوان لإعادة تلوين النوافذ بألوان داكنة والخطوط بألوان فاتحة مناسبة، مع الاحتفاظ بتناسق بقية التصميم.

### التجربة الإجمالية:

يهدف التصميم إلى جعل المستخدم يشعر بأن التطبيق "_مساعد شخصي_" له. لذلك قد تتضمن الواجهة لمسات ودية مثل **رسائل تشجيعية** تظهر في لوحة التحكم (مثال: "أحسنت! أنجزت 5/5 مهام اليوم" أو "تبقى عادة واحدة لتحقيق سلسلة مثالية هذا الأسبوع!"). هذه العناصر تعزز الارتباط العاطفي للمستخدم بالتطبيق وتحفزه على الاستمرار في الاستخدام اليومي.

بشكل عام، تم تحقيق التوازن في التصميم بين الجانب الوظيفي (الحصول على المعلومات المطلوبة بسرعة وسهولة) والجانب الجمالي (مظهر أنيق حديث يشجّع على الاستخدام). وقد تم الالتزام بمعايير **واجهات الاستخدام الرسومية GUI** الشائعة وأدلّة الاستخدام لـ WPF، لضمان أن التطبيق يبدو **احترافيًا** كما لو كان وثيقة SRS حقيقية تحوّلت إلى منتج ملموس.

## 8. تحليل الأمان (Security Analysis)

يشمل الجانب الأمني في هذا النظام حماية **وصول المستخدم وبياناته الشخصية** على عدة مستويات، نظرًا لطبيعة المعلومات الحساسة المحتملة (عادات شخصية، مذكرات مهام، تقدم دراسي...).

### أمان عملية تسجيل الدخول:

عند تشغيل التطبيق، يواجه المستخدم شاشة تسجيل الدخول التي تطلب إدخال كلمة المرور الأساسية. هذه الكلمة (الافتراضية 1141004) يجب عدم تخزينها كنص صريح في الكود أو في أي ملف إعدادات بشكل يسهل قراءته. بدلاً من ذلك، يمكن تخزين **تجزئة مشفرة (Hashed)** من كلمة المرور في قاعدة البيانات أو في إعدادات التطبيق، ومقارنة تجزئة ما يدخله المستخدم مع التجزئة المخزنة للتحقق. نظراً لأن التطبيق لسطح المكتب ومن فرد واحد، قد يكفي تضمين التحقق ضمن التطبيق، لكن في حال تمكين تغيير المستخدم لكلمة المرور، ينبغي تخزينها مجزأة salt+hash. علاوة على ذلك، من المستحسن حماية التطبيق من محاولات التخمين: مثلاً بعد 5 محاولات خاطئة متتالية يمكن غلق التطبيق لفترة قصيرة أو تحذير المستخدم. هذا يمنع **الهجمات بالقوة الغاشمة** لتخمين كلمة السر.

### الكلمات السرية المخفية:

ذكر أن هناك كلمات مرور خفية تؤدي إلى تفعيل أوامر سرية. يجب التعامل مع هذه الآلية بحذر لضمان عدم استغلالها بشكل غير مقصود. المقترح هو أن تلك الكلمات الخاصة لا تفتح وظائف خطرة فقط، بل ايضا ربما تكشف _وضع المطوّر_ أو معلومات إضافية للمستخدم المتقدم. يتم تعريف هذه الكلمات في منطق البرنامج وقد تكون **مشفرة أو مبهمة** لكي لا يستطيع أي مستخدم استخراجها بسهولة من الملف التنفيذي (مثلاً عبر الهندسة العكسية). كما يمكن أن تكون هذه الكلمات **مرتبطة بحسابات محددة** - لكن بما أن التطبيق أحادي المستخدم، فالمهم أن لا تؤدي هذه الأوامر المخفية إلى خرق أمني. على سبيل المثال، إذا كانت إحدى الكلمات السرية تظهر لوحة تحكم مخفية، فيجب ألا تسمح بتعديل بيانات دون رقابة أو تجاوز التحقق. يمكن أيضًا وضع سجل أحداث (log) داخلي يسجل متى تستخدم كلمة سرية، لأغراض المراقبة debugging.

### حماية البيانات المخزنة:

جميع البيانات (المهام، العادات، إلخ) مخزنة في **ملف قاعدة بيانات SQLite** على الجهاز. بشكل افتراضي، هذا الملف يمكن لأي برنامج قراءته إذا وصل لمساره. لتعزيز الأمان، هناك خيارات منها: - **تشفير قاعدة البيانات**: توفر SQLite إمكانية التشفير الكامل عبر امتدادات مثل SQLite Encryption Extension أو استخدام مكتبات تشفير مفتوحة المصدر. هذا يعني أنه حتى لو حصل شخص على ملف .db فلن يستطيع قراءته بدون مفتاح التشفير الذي يمكن اشتقاقه من كلمة مرور التطبيق. - **تحديد أذونات الوصول للملف**: بما أن التطبيق مكتبي، يمكن حفظ ملف قاعدة البيانات في مجلد خاص بالمستخدم مع ضبط صلاحيات نظام التشغيل بحيث لا يتمكن مستخدمون آخرون في نفس الحاسوب من فتحه. - **حماية النسخ الاحتياطية**: إذا وفر التطبيق ميزة لتصدير البيانات (كملف backup مثلاً)، فيجب تشفير هذا الملف أو حمايته بكلمة مرور عند إنشائه. - **منع إدخال البيانات الضارة**: تأمين طبقة قاعدة البيانات يشمل أيضًا استخدام استعلامات محضّرة (parameterized queries) أو ORM لمنع _هجمات SQL Injection_ حتى لو كان السيناريو هنا محدود (لا مدخلات من مصادر خارجية تقريبًا، لكن الاحتياط واجب).

### خصوصية المستخدم:

قد تتضمن البيانات المخزنة ما يشير إلى _سلوكيات المستخدم وعاداته اليومية_، وهي تعتبر بيانات شخصية. يجب الحرص على أن التطبيق لا يشارك هذه البيانات مع أي جهة خارجية دون إذن صريح. حالياً التطبيق **غير متصل بالإنترنت** (Standalone)، مما يقلل مخاطر تسريب البيانات عبر الشبكة. مع ذلك، أي تكامل مستقبلي (كالمزامنة السحابية) يجب أن يحترم مبادئ **الخصوصية وفق القوانين** (مثل GDPR إن توسع النطاق) بحيث يتم تشفير البيانات أثناء النقل وتخيير المستخدم بوضوح بشأن ما يتم رفعه.

### الدفاع ضد الأخطاء والحالات غير المتوقعة:

من منظور الأمان التشغيلي، يجب أن يتعامل التطبيق بأمان مع الأخطاء بحيث لا يؤدي خطأ إلى كشف معلومات أو فقدانها. مثلاً، إذا حدث فشل أثناء حفظ مهمة، ينبغي عدم انهيار التطبيق بل تنبيه المستخدم بشكل مناسب وحفظ العمل المؤقت إن أمكن. كما يجب **التحقق من صحة المدخلات** (Validation) لكل الحقول (عدم السماح بنصوص غريبة قد تسبب سلوكًا غير متوقعًا، تقييد أطوال الحقول، إلخ) لمنع استغلال أي ثغرة محتملة من خلال الحقول النصية.

### التحكم بالوصول الداخلي:

بما أن التطبيق لمستخدم واحد، لا يوجد مفهوم تعدد المستخدمين بأدوار. ولكن يمكن التفكير مستقبلاً في دور "مسؤول/مشرف" للتطبيق نفسه (مثلاً إن استُخدم ضمن شركة). حاليًا، يتمثل التحكم بالوصول الداخلي في عزل وظائف الميزات السرية عن الاستخدام العرضي. مثلاً، عدم عرض واجهة الميزات المخفية إلا بعد التفعيل الصريح.

### تحديثات التطبيق:

من الجيد ضمن الاعتبارات الأمنية التخطيط لآلية تحديث البرنامج. في حال اكتشاف ثغرة أو مشكلة، ينبغي توفير طريقة آمنة للمستخدم لتحديث نسخته. يمكن للتطبيق مثلاً أن يتحقق من وجود إصدار جديد (لو كان متصلاً) أو على الأقل فريق التطوير يوفر تحديثات يدوية مع إرشادات. أثناء التحديث، يجب عدم فقدان البيانات أو كشفها، لذا يمكن أن يتم **ترحيل قاعدة البيانات** بطريقة سليمة في حال تغير هيكلها.

باختصار، النهج الأمني هنا استباقي: نفترض وجود مخاطر محتملة (وإن كانت منخفضة) ونعمل على تخفيفها. بفضل الطبيعة غير الشبكية للتطبيق يمكن تركيز الجهد على حماية الجهاز نفسه والبيانات المحلية بكلمات مرور وتشفير. كما أن إلمام المستخدم بالمخاطر مهم؛ قد نشمل في التوثيق قسمًا ينصح المستخدم بحماية جهازه بكلمة مرور نظام التشغيل وعدم مشاركة ملف قاعدة البيانات. **أمان المعلومات** عنصر أساسي في ثقة المستخدم بتطبيق لإدارة حياته، لذا تمت مراعاة ذلك طوال دورة تطوير المشروع.

## 9. تحليل قاعدة البيانات (Database Analysis)

يعتمد التطبيق قاعدة بيانات محلية من نوع **SQLite** لتخزين المعلومات المنظمة. تتميز SQLite بخفة الوزن وعدم حاجتها إلى خادم خارجي، مما يجعلها مناسبة تمامًا لتطبيق مكتبي شخصي. تم تصميم هيكلية قاعدة البيانات لتدعم الأقسام الرئيسية (العادات، الكورسات، التقويم، المستخدم/الإعدادات). يوضح المخطط التالي (ERD) الكيانات الرئيسية وعلاقتها:

_مخطط ERD مبسط يبين هيكل قاعدة البيانات:_

·       **جدول Habit (العادات)**: يحتفظ بمعلومات كل عادة يُعرّفها المستخدم. من الأعمدة: معرف العادة HabitID (المفتاح الأساسي)، اسم العادة Name، نوعية التكرار أو الهدف Frequency/Goal (مثلاً "يومي" أو "3 مرات أسبوعياً" أو هدف رقمي كعدد صفحات يومي).

·       **جدول HabitLog (سجل إنجاز العادات)**: يرتبط بكل عادة بمجموعة من السجلات اليومية أو الدورية. يتضمن: معرف سجل LogID (أساسي)، مرجع إلى معرف العادة HabitID (مفتاح أجنبي يشير إلى Habit)، التاريخ Date، وحالة الإنجاز أو القيمة Status/Value (قد تكون نعم/لا إذا العادة ثنائية الإنجاز، أو رقم مثل كمية الماء باللتر). العلاقة بين Habit و HabitLog هي واحد إلى متعدد؛ أي كل عادة لها عدة سجلات (سجل لكل يوم عادةً). هذا التصميم يمكّن من تتبع تاريخ أداء العادة وبناء إحصائيات حولها بسهولة.

·       **جدول Course (الكورسات)**: لتخزين بيانات الدورات التعليمية. الحقول: CourseID (أساسي)، عنوان الكورس Title، درجة التقدم Progress (بنسبة مئوية أو عدد الوحدات المكتملة من الإجمالي)، وربما تفاصيل أخرى مثل Category التصنيف أو Provider الجهة المقدمة في حال الحاجة. يمكن لاحقًا إضافة جدول فرعي لوحدات الكورس (Modules) إن احتجنا لتفصيل أكثر، لكن في النموذج الحالي نبقيها بسيطة بتسجيل نسبة التقدم مباشرة في الجدول الرئيسي.

- **جدول CalendarEvent (الأحداث في التقويم)**: هذا الجدول يحفظ المهام والأحداث المجدولة. يتضمن: EventID (أساسي)، عنوان الحدث Title، تاريخ/وقت الحدث DateTime، نوع الحدث Type. حقل "النوع" قد يُستخدم لتمييز ما إذا كان هذا الحدث مرتبطًا بقسم معين (مثلاً "مهمة عمل" أو "موعد شخصي" أو "درس كورس" أو ربما "عادة" إذا مثلنا العادات في التقويم). يمكن وجود أعمدة إضافية مثل الوصف التفصيلي Description أو مستوى الأهمية Priority، لكن التركيب الأساسي المذكور يغطي الاحتياج الرئيسي (تحديد متى وماذا سيحدث). حاليًا العلاقة بين الأحداث وباقي الجداول غير مباشرة – أي أنه لا توجد مفاتيح أجنبية تربط الحدث مثلاً بكورس معين (إلا إذا قررنا استخدام Type كمفتاح لربط CourseID عند نوع=Course). في التصميم البسيط، يتم الربط منطقيًا عبر الاسم أو يعرض كحدث منفصل حسب ما يدخله المستخدم.
- **جدول User أو Settings**: نظرًا لوجود مستخدم واحد، يمكن استخدام جدول لتخزين إعدادات التطبيق (مثل تفعيل الوضع الليلي، آخر عملية مزامنة، ... إلخ). وقد يحتوي على حقل لكلمة المرور (مشفرة) إذا سمح بتعديلها. ولكن في حالة بسيطة، قد لا نستخدم جدول مستخدم منفصل بل نخزن الإعدادات في ملف إعدادات خارجي أو ضمن مجالات SQLite مختلفة.

بعض النقاط حول تصميم قاعدة البيانات: - استخدمنا **معرّفات رقمية** أساسية (INTEGER PRIMARY KEY) لكل جدول لتسهيل العلاقات. SQLite يُنشئها كـrowid إذا لم نحدد، لكن هنا حددنا أسامي المعرفات لزيادة الوضوح. - العلاقات: العلاقة الوحيدة المباشرة المعرّفة في المخطط هي بين Habit و HabitLog (واحد إلى متعدد). العلاقات الأخرى ضمنية: فمثلاً حدث التقويم قد يرتبط باسم كورس عبر اشتراك اسم، لكن ليس مفتاح أجنبي صريح (إلا إذا توسع التصميم). هذا القرار يجعل التصميم أبسط وأكثر مرونة لأن حدث التقويم يمكن أن يمثل أي شيء بلا تقييد، لكنه يُفقدنا بعض تكامل البيانات (Data integrity) – يمكن لاحقًا تحسين ذلك بإضافة ربط، أو تصميم **جدول وسيط** إذا رغبنا بربط كورس بأحداث متعددة (كثير إلى كثير). - **الفهارس (Indexes)**: سيتم إنشاء فهرس تلقائيًا للحقول الأساسية (مفاتيح أساسية). قد نضيف فهارس على حقول مثل تاريخ الحدث لتسريع الاستعلامات (مثل عرض أحداث شهر معين)، أو على HabitID في جدول HabitLog لتسريع جمع سجلات عادة واحدة. - **سلامة البيانات**: اعتمدنا أنواع SQLite المناسبة: INTEGER للمعرفات، TEXT للأسماء والوصف، DATE أو DATETIME للتواريخ (بالإمكان تخزينها كنص ISO8601 مثلاً). بالنسبة لحقل Status في سجل العادات، إن كان ثنائي (منجز/غير منجز) يمكن استخدام نوع BOOLEAN (SQLite لا يملك نوع منطقي حقيقي لكنه يقبل 0/1). أما لو مثل قيمة رقمية (كم صفحة قرأ) فيكون INTEGER أو REAL. - **التمدّد المستقبلي**: تصميم الجداول الحالي قابل للتوسع. مثلاً، يمكن إضافة عمود "Notes" على حدث التقويم لاحقًا لحفظ تفاصيل، أو إضافة جدول "Tasks" منفصل لو أردنا تخزين مهام بغير كونها أحداث تقويم (مثلاً مهام قائمة待办 to-do list عادية). حاليًا يمكن تمثيل هذه المهام في جدول CalendarEvent بدون وقت (أو نعتبر الوقت = موعد نهائي). - **التكامل مع الإحصائيات**: غالبية الإحصائيات ستُستخرج عن طريق استعلامات. مثلاً، لحساب نسبة الإنجاز عادة في شهر، سنستخدم جدول HabitLog لعد الأيام المنجزة. لتحليل المهام المكتملة سنحتاج جدولًا للمهام… يمكن اعتبار CalendarEvent يخزن المهام مع حالة (منجز أم لا). ربما يلزمنا حقل Done (منطقي) في CalendarEvent لوصف إتمام المهمة. في حالة غياب ذلك، يمكننا اعتبار حدث التقويم دائمًا تم (باعتباره حدثًا حصل) ولكن هذا لا يغطي _مهام_ قد تكتمل أو لا. لذا قد يكون من الأفضل هيكلة المهام ككيان منفصل عن الأحداث (ولكن هنا افترضنا أن كل مهمة هي حدث بتاريخ إنجاز). يمكن للتبسيط إضافة عمود IsDone إلى CalendarEvent وتحديثه عند إكمال المهمة.

كل هذه التفاصيل تم التفكير فيها للحفاظ على **تكامل واتساق البيانات** وفي نفس الوقت **بساطة التصميم** ليتلاءم مع نطاق المشروع الحالي. تصميم قاعدة البيانات بهذا الشكل يسمح باسترجاع البيانات بكفاءة لتظهيرها في واجهات المستخدم المختلفة، كما أنه مرن لإضافة روابط إضافية بين الجداول إذا قررنا في المستقبل توسيع الترابط (مثلاً ربط مهمة معينة بعادة بحيث يؤثر إنجاز المهمة على سجل العادة والعكس).

## 10. مخططات UML (النمذجة الموحدة)

للمساعدة في توثيق وفهم النظام، سنعرض ثلاثة أنواع من مخططات UML: **مخطط حالات الاستخدام (Use Case Diagram)** لتوضيح تفاعلات المستخدم مع وظائف النظام، **مخطط الصنف (Class Diagram)** لتبيان البنية الهيكلية للأصناف وعلاقاتها، و**مخطط التسلسل (Sequence Diagram)** لإظهار تتابع الرسائل في سيناريو محدد (كتسلسل عملية تسجيل الدخول).

### 10.1 مخطط حالات الاستخدام (Use Case Diagram)

يوضح مخطط حالات الاستخدام الجهات الفاعلة (actors) – وغالبًا هنا الممثل الوحيد هو "المستخدم" نفسه – والوظائف أو السيناريوهات التي يمكنه القيام بها ضمن النظام. فيما يلي قائمة بحالات الاستخدام الرئيسية للمستخدم في هذا التطبيق: - **تسجيل الدخول إلى النظام**: يقوم المستخدم بإدخال كلمة المرور للوصول إلى الوظائف. _حالة استخدام بديلة_: إدخال كلمة سر خاصة لتفعيل وضع خفي (Actor هنا ما زال المستخدم نفسه ولكن نتيجة مختلفة). - **إضافة عادة جديدة**: إدخال تفاصيل عادة ليقوم النظام بتسجيلها والبدء بتتبعها. - **تسجيل إنجاز عادة**: كل يوم أو فترة يقوم المستخدم بتعليم العادة كمنجزة في سجل ذلك اليوم. - **إدارة العادة**: تشمل تعديل إعدادات عادة أو حذفها. - **إضافة كورس جديد**: تسجيل دورة تعليمية جديدة ضمن قائمة الكورسات. - **تحديث تقدم كورس**: تعديل نسبة الإنجاز أو وسم جزء كمكتمل. - **تنظيم حدث/مهمة في التقويم**: إضافة مهمة جديدة مع تاريخ ووقت. (يتضمن ربما ربطها بكورس أو تصنيف معين). - **تعديل/حذف حدث من التقويم**: تغيير موعد أو تفاصيل حدث قائم أو إزالته. - **عرض الإحصائيات**: قيام المستخدم بطلب عرض لوحة الإحصاءات، حيث يقوم النظام تلقائيًا بحساب المؤشرات وعرضها. - **تخصيص الإعدادات**: تغيير سمة اللون أو تفعيل/إلغاء تنبيهات أو تغيير كلمة المرور الأساسية. - **تفعيل وظيفة مخفية**: إدخال المستخدم لكلمة مرور خاصة عند شاشة الدخول (أو مكان آخر مخصص) لتفعيل أمر سري (مثلاً "عرض وضع المطور").

في مخطط حالات الاستخدام، جميع هذه الحالات متصلة بالمستخدم الذي هو **Actor رئيسي**. قد يوجد Actor آخر نعتبره ضمنيًا وهو "نظام التقويم الخارجي" إن فكرنا في المزامنة، لكن حاليًا خارج النطاق. لذا فالمستخدم البشري هو من يبدأ كل الحالات. حالات الاستخدام يمكن أن يكون بينها علاقات _include_ أو _extend_. مثلاً: - حالة "تسجيل إنجاز عادة" _تتضمن_ ضمنيًا التحقق من التاريخ الحالي وتحديث السجل (وهي تفاصيل داخلية). - حالة "تفعيل وظيفة مخفية" هي **امتداد** أو سيناريو بديل لعملية تسجيل الدخول القياسية: أي _امتداد_ يحدث فقط إذا تم إدخال كلمة معينة، فتكون نتيجة تسجيل الدخول مختلفة (كتمكين خيارات إضافية في الواجهة أو فتح نافذة مخفية).

يساعد هذا المخطط في ضمان أننا غطّينا كل التفاعلات المطلوبة. على سبيل المثال، إذا نسينا حالة استخدام "تحديث تقدم كورس"، سيتبين لنا من المخطط وجود فجوة ويجب إضافة هذه الميزة. وبالعكس، لو وُجدت حالة استخدام غير مدعومة بخاصية فعلية في المتطلبات، فإما أن نخطط لإضافتها أو نزيلها من التصميم إن كانت خارج النطاق.

### 10.2 مخطط الصنف (Class Diagram)

مخطط الصنف يصوّر الأصناف (Classes) في النظام وعلاقات الوراثة أو الارتباط بينها، مع الخصائص والعمليات الرئيسية لكل صنف. بناءً على التحليل السابق، يمكننا تحديد أبرز الأصناف: - **Habit**: يمثل العادة ككائن في التطبيق. الخصائص: ID, Name, Frequency, وربما CurrentStreak (سلسلة الإنجاز الحالية) كخاصية محسوبة. العمليات (الأساليب): قد تشمل MarkAsDone(date) لوضع سجل إنجاز في تاريخ معين، أو ResetStreak() عند كسر الاستمرارية. - **HabitLogEntry**: يمثل سجل فردي لإنجاز عادة بتاريخه. الخصائص: HabitID, Date, Done (boolean) أو Value. يمكن اعتباره كائن قيمة Value Object يرتبط بكائن Habit. ربما لا يحتاج عمليات معقدة بل هو حاوية بيانات. - **Course**: يمثل الكورس التعليمي. الخصائص: ID, Title, Progress, TotalUnits. العمليات: UpdateProgress(unitsCompleted) لتحديث نسبة الإنجاز (ستغير Progress). - **CalendarEvent**: يمثل حدث التقويم/المهمة. الخصائص: ID, Title, DateTime, Type, IsDone. العمليات: MarkDone() لوضع الحدث كمكتمل (في حالة كان مهمة قابلة للإنجاز). - **UserSettings**: يمثل إعدادات المستخدم. الخصائص: مثل Theme, NotificationsEnabled, PasswordHash. العمليات: ربما ChangePassword(old, new), ToggleTheme().

بالإضافة إلى ذلك، في الجانب البرمجي (وليس قاعدة البيانات فقط): - **LoginManager** أو **AuthService**: لإدارة عملية التحقق من كلمة المرور. قد يكون كلاس يحوي دالة ValidatePassword(input) والتي تعود بنتيجة (صحيح/خطأ أو نوع الدخول - عادي أم مشرف). - **SecretCommandManager**: كلاس مسؤول عن معرفة الكلمات السرية وتفعيل ما يلزم. مثلاً يحتوي قائمة بالكلمات الخاصة وما تقابله من أوامر، ودالة CheckSecret(input) تعود بنوع الأمر أو null إن لم يكن خاصًا. - **StatisticsGenerator**: كلاس مساعد لجمع البيانات من Habit, Course, Event وتوليد إحصائيات. مثلاً أسلوب GetHabitCompletionRate(habit, dateRange) يحسب النسبة، أو GetTasksDonePerDay(range) يرجع مصفوفة بيانات لرسم مخطط. - **ViewModels**: مثل HabitsViewModel, CoursesViewModel, CalendarViewModel, StatsViewModel، التي تربط بين الأصناف السابقة والواجهة. هذه ربما تظهر في مخطط الصنف ولكن عادة نمثّل فقط الطبقة المنطقية/البيانات. مع ذلك يمكن إظهار ViewModel رئيسي لكل قسم وعلاقته بالأصناف، كأن يحتوي على قوائم: List<Habit> في HabitsViewModel وهكذا.

**العلاقات**: من الواضح وجود **ترابط Composition** بين Habit و HabitLogEntry (العادة تحتوي سجلاتها، وتموت السجلات إذا حذفت العادة). كذلك Course قد يرتبط بعلاقة تكوين مع CalendarEvent إذا اعتبرنا كل حدث درس هو جزء من كورس، لكن هذا ليس صريح في هذا النسق (يمكن أن نخطه كارتباط association: حدث قد يشير إلى كورس عبر حقل type/ID). UserSettings ممكن اعتباره وحيد (Singleton) مرتبط بالمستخدم.

أما بين الـViewModels والأصناف: ViewModel يحتوي مراجع للأصناف أو لتوابع الوصول للبيانات. مثلاً HabitsViewModel قد يحتوي خاصية Habits: ObservableCollection<Habit>، و SelectedHabit: Habit، ووظيفة AddHabit() التي تستدعي ربما مخزن بيانات أو تضيف للكولكشن مع تحديث قاعدة البيانات.

بما أن التطبيق بسيط، لا يوجد وراثة inheritance كبيرة بين هذه الأصناف، ربما ماعدا ViewModels يمكن أن ترث من قاعدة BaseViewModel لتشارك وظيفة OnPropertyChanged وهكذا. الأصناف البيانات كـHabit, Course لا ترث من شيء سوى ربما INotifyPropertyChanged إذا جعلناها قابلة للربط مباشرة.

مخطط الصنف يساعدنا في التأكد من شمولية التصميم: هل لدينا صنف يغطي تمثيل كل جزء في المتطلبات؟ أيضًا يعيننا على عدم الازدواجية (مثل عدم وجود صنفين يقومان بمهام متماثلة بلا داع). يظهر المخطط العلاقات بحيث ننتبه – مثلاً – لعدم وجود رابط بين كورس وأحداثه ربما قررنا إضافته لسلامة أكثر. أو يذكرنا بأن الإحصائيات تعتمد على وجود ارتباطات معينة.

### 10.3 مخطط التسلسل (Sequence Diagram)

سنقوم بتمثيل سيناريو حيوي كمخطط تسلسل يوضح التفاعل بين العناصر بمرور الوقت. من السيناريوهات الأساسية: **عملية تسجيل الدخول مع التحقق من كلمة المرور وربما تفعيل وضع خاص**.

خطوات هذا السيناريو كتابيًا: 1. يبدأ المستخدم (Actor) بفتح التطبيق، فيظهر له _نافذة تسجيل الدخول_ (LoginView). 2. يقوم المستخدم بإدخال كلمة المرور في مربع النص ويضغط زر "دخول". 3. يلتقط LoginViewModel الحدث (LoginCommand)، ويرسل الرسالة ValidatePassword(password) إلى كائن مسؤول عن المصادقة (AuthService). 4. يقوم AuthService بفحص الكلمة المدخلة: - إذا طابقت الكلمة السرية الأساسية الصحيحة (بعد التجزئة مثلاً)، يعيد نتيجة "نجاح" عادي. - إذا طابقت إحدى **الكلمات السرية الخاصة** المحددة، يمكن أن يعيد نتيجة "نجاح-وضع-خاص" أو يضبط علامة في النظام أن وضعًا مخفيًا يجب تفعيله - إذا لم تطابق، يعيد "فشل". 
5. يستقبل LoginViewModel النتيجة: - في حالة النجاح العادي: يرسل رسالة إلى الـMainView (النافذة الرئيسية) للفتح، وربما يمرر معلومات المستخدم (لا يوجد اسم مستخدم هنا، لكن قد يمرر مؤشر على الوضع). - في حالة نجاح مع وضع خاص: نفس الشيء، لكن أيضًا ينادي SecretCommandManager ليتم تفعيل الميزات الخاصة (مثلاً ضبط متغير static أن الوضع الخاص مفعل). ثم يفتح MainView مع تمكين عناصر كانت مخفية (يمكن مثلاً عبر قراءة ذلك المتغير في وقت التحميل). - في حالة الفشل: يرسل LoginViewModel إشعارًا لـLoginView لعرض رسالة خطأ للمستخدم "كلمة المرور غير صحيحة"، وربما يزيد عداد المحاولات. لا يغلق النافذة بل يسمح بإعادة المحاولة. 6. يظهر للمستخدم إثر نجاح الدخول الـMainView مع أقسام التطبيق. لو كان وضع خاص، قد يرى المستخدم مؤشرات خاصة (كظهور قائمة إضافية في شريط القوائم خاصة بالمطور مثلاً). 7. ينتهي التسلسل هنا بانتقال التحكم إلى الشاشة الرئيسية.

في مخطط التسلسل UML، سنمثل: - الكائن Actor: _User_ عند الطرف الأيسر يبدأ التفاعل. - الكائنات المشاركة: LoginView (واجهة)، LoginViewModel, AuthService, وربما SecretCommandManager، وأخيرًا MainView. - يسلسل الخط الزمني عموديًا مع الرسائل: - User -> LoginView: حدث "ضغط زر تسجيل الدخول". - LoginView -> LoginViewModel: استدعاء الأمر مع كلمة المرور (يمكن القول loginViewModel.Login(password)). - LoginViewModel -> AuthService: استدعاء ValidatePassword(password). - AuthService -> (داخلي) Database ربما، أو إلى نفسه: التحقق من التجزئة مقابل المخزن. هنا يمكن إظهار نقطة قرار (Alt frame) بثلاث حالات: كلمة عادية، كلمة خاصة، كلمة خاطئة. - AuthService يعود بنتيجة (return AuthResult). - LoginViewModel -> SecretCommandManager: (في حال خاصة) تفعيل الخاصية ActivateSecretMode(secretCode) مثلاً. - LoginViewModel -> MainView: إنشاء/فتح النافذة الرئيسية (يمكن اعتباره رسالة ShowMainView(isSecretMode)). - MainView -> MainView: يقوم بتهيئة نفسه، قد يستدعي LoadData() من قواعد البيانات (يمكن تضمين رسالة إلى Database أو HabitsRepository لجلب البيانات). - LoginView يمكن أن يدمر نفسه أو يختفي (رسالة deactivate). - ينتهي السيناريو مع استمرار جلسة المستخدم في الـMainView.

هذا المخطط يعطينا فهما لتسلسل الأحداث البينيّة. مثلاُ نتأكد أن: - مسؤولية التحقق الأمني معزولة في AuthService وليس في واجهة الدخول. - بعد التحقق، كيف يتحدد تفعيل الوضع الخاص – عبر SecretCommandManager (الذي ربما يحتوي جدولة أو أوامر تضاف للواجهة). - إدارة الواجهة (فتح الرئيسية وغلق نافذة الدخول) يقوم بها الـViewModel أو جزء من البنية، وعلينا التأكد من أن لدينا آلية لذلك (مثل حدث أو كولباك).

مخطط تسلسل آخر يمكن تصوره هو **إضافة مهمة من التقويم**: User يضغط إضافة -> تظهر نافذة المهمة -> يدخل بيانات -> ViewModel يرسل لـEventRepository -> يحفظ ويعيد ID -> يضيف ViewModel الحدث لقائمة الأحداث -> يظهر في التقويم. هذا أيضًا مهم لكن كأمثلة نكتفي بواحد مفصل.

استخدام هذه المخططات أثناء التحليل والتصميم ساعد على التأكد من تماسك تدفق العمليات وعدم نسيان أي خطوة وسيطة مهمة. كما أنها ستكون جزءًا من **وثيقة SRS** الرسمية لشرح كيفية عمل النظام داخليًا لكل من المصممين والمطورين في الفريق.

## 11. واجهة تسجيل الدخول (آلية وعناصر مخفية)

تعتبر **واجهة تسجيل الدخول** البوابة الأساسية للوصول إلى التطبيق، ولذلك تم تصميمها بعناية من حيث الشكل والأمان والوظائف الخاصة المرتبطة بها.

### التصميم والشكل العام:

الواجهة بسيطة واحترافية. تظهر نافذة صغيرة في وسط الشاشة تحتوي على: - شعار أو اسم التطبيق في الأعلى بشكل جذاب لتحديد هوية البرنامج (مثال: "OrganizerPro" أو أي اسم مشروع). - حقل إدخال كلمة المرور (من نوع _PasswordBox_ في WPF لضمان إخفاء المدخلات كنقاط). - زر "دخول" واضح للمستخدم، مع إمكانية تنشيطه بالضغط على مفتاح Enter أيضًا لتسهيل الاستخدام. - ربما **تلميح تفاعلي** يظهر أسفل الحقل لتوجيه المستخدم ("أدخل كلمة المرور للمتابعة")، ويتحول إلى رسالة خطأ باللون الأحمر إذا فشل تسجيل الدخول ("كلمة المرور غير صحيحة، حاول مرة أخرى"). - الواجهة ذات طابع حديث؛ الخلفية قد تكون بلون محايد أو تحتوي تدرجًا بسيطًا، مع أيقونة قفل بجانب حقل كلمة المرور للإيحاء بوظيفته.

### كلمة المرور الأساسية:

الكلمة الأساسية المعرفة افتراضيًا هي 1141004. عند تشغيل أول مرة، قد تكون هذه هي القيمة المطلوبة. يمكن أن يتم تشفيرها ضمن التطبيق - مثلاً مخزنة كتجزئة SHA256 لسلسلة "1141004" - ويتم مقارنة ما يدخله المستخدم بعد تجزئته بتلك القيمة. بهذه الطريقة حتى لو اطلع أحدهم على قاعدة البيانات فلن يحصل على النص الصريح. إذا رغب المستخدم، ضمن **الإعدادات** يمكن توفير خيار تغيير كلمة المرور الرئيسية. في تلك الحالة يتم تحديث التجزئة المخزنة واعتماد الكلمة الجديدة لاحقًا.

### الكلمات المرورية المخفية (Secret Passwords):

تمت برمجة واجهة تسجيل الدخول للتعرّف أيضًا على بعض **الكلمات الخاصة** التي إذا تم إدخالها بدلاً من كلمة المرور الأساسية، فإنها في بعض الاحيان لا تفتح التطبيق بالشكل العادي، وإنما **تفعّل أوامر/وظائف سرية**. هذه الوظائف تم تضمينها كنوع من Easter Egg أو أدوات مطور او اختصارات سرية. أمثلة على تلك الكلمات ووظائفها الممكنة: - عند إدخال كلمة سر معينة (مثلاً "ADMINMODE"): يدخل التطبيق في _وضع المطور_ حيث تنكشف بعض المعلومات الإضافية في الواجهات (مثل عرض جميع الجداول raw debugging info في قسم الإحصائيات، أو إتاحة زر لتعديل قاعدة البيانات يدويًا). هذا الوضع يساعد المطور/المستخدم المتقدم على تفحص حالة التطبيق وربما تصحيح أخطاء البيانات. - كلمة سر أخرى (مثلاً "RESETDATA"): قد تكون مخصصة **لإعادة ضبط** التطبيق. إذا استخدمت، يمكن أن يظهر تأكيد قبل التنفيذ، ثم يتم أرشفة أو حذف بيانات قاعدة البيانات وإعادة إنشاءها (مفيد للاختبار أو بدء مستخدم جديد دون بيانات). - كلمة خاصة (مثلاً "SHOWMETHEMONEY"): ربما تفعيل _وضع تجريبي ممتع_ كتغيير الثيم إلى ألوان مزح أو إظهار شخصية كرتونية في الواجهة. هذه ليست وظيفة جدية لكن تضيف جانبًا مرحًا أحيانًا ،او مثلا كود سري يقوم بمسح قاعدة البيانات او مثلا كود سري يقوم بإعطاء رسالة تمويه يكون المستخدم قد كتبها مسبقاً.

تفعيل هذه الأوامر السرية بعد التحقق منها يتم كما شرحنا في مخطط التسلسل: حيث يعيد AuthService رمزًا يدل على التعرف على كلمة خاصة. ويمكن في البنية ضبط متغير عام يشير إلى الوضع الحالي (كحالة application state). عند الانتقال للنافذة الرئيسية، يقوم التطبيق بفحص هذا العلم لتحديد ما إذا كان يجب **تمكين خصائص معينة**. مثلاً:

if(AppState.IsDevMode) { developerMenu.Visible = true; }

أو ربما في قسم الإحصائيات:

if(AppState.IsDevMode) { ShowRawDataTab(); }

وهكذا.

### ضمان السرية والتحكم:

من المهم ألا تكون هذه الكلمات السرية سهلة التخمين، وألا تكون موثّقة صراحة للمستخدم العادي (لأنها غير مطلوبة لاستخدام التطبيق اليومي). تم اختيار كلمات يصعب أن تُدخل بالصدفة. كذلك، تم تصميم تنفيذها بحيث **لا تؤثر سلبًا** على المستخدم إن لم يكن يعرفها: أي لن يفتح شيء غريب أو يحذف بيانات بدون قصد. مثلاً لو أدخل المستخدم كلمة خاطئة تماماً، سيعامله النظام كحالة كلمة مرور غير صحيحة فقط.

الكلمات السرية ومخرجاتها أيضًا يمكن تأمينها بصلاحيات إضافية إن لزم. لكن باعتبار التطبيق شخصي، هذه الأوامر موجودة أساسًا لغايات التطوير/الاختبار أو لإضافة حماس للمستخدم الخبير. وربما يتم إعلام المستخدم الخبير بوجودها عبر الوثائق التقنية، لكن المستخدم العادي ليس بحاجة لمعرفتها.

### معالجة حالات الاستخدام:

إذا أدخل المستخدم كلمة المرور الصحيحة -> ينتقل مباشرة. إذا أدخل كلمة خاصة صحيحة -> يدخل وينفذ الأمر الخاص تلقائياً. مثلاً: - في حالة وضع المطور، قد يظهر إشعار "وضع المطور مفعل" لتأكيد ذلك. - في حالة إعادة الضبط، ربما لا يذهب إلى النافذة الرئيسية بل ينفذ إعادة الضبط ثم يعود لشاشة الدخول منتظراً كلمة مرور عادية (مع إشعار "تمت إعادة الضبط"). - في حالة أوامر غير فورية (كالوضع المرح)، يدخل التطبيق مع التغيير المطلوب (مثلاً الثيم الملون) جاري بالفعل.

إذا أدخل كلمة خاطئة -> رسالة الخطأ وعداد المحاولات. لو تجاوز الحد (مثلاً 5)، يمكن أن يقفل زر الدخول لـ30 ثانية أو يطالب المستخدم بتأكيد (كابتشا بسيطة) ليتأكد أنه الشخص نفسه وليس برنامج آلي.

### تجربة المستخدم:

حاولنا جعل واجهة الدخول سريعة وغير معقدة. كونها مجرد كلمة مرور واحدة (لا اسم مستخدم)، فهي أسرع بكثير. وفي ذات الوقت، هذا يعني أي شخص يعرف تلك الكلمة يمكنه الدخول؛ لذا أكدنا على أهمية عدم مشاركة المستخدم لكلمة مروره. في التطبيقات المستقبلية، ربما إضافة عامل مصادقة ثاني (كإرسال رمز OTP للهاتف) سيكون أكثر أمانًا، لكن هنا نرى أنه زائد عن الحاجة لتطبيق غير متصل.

خلاصة القول: واجهة تسجيل الدخول تعمل كحارس بوابة يضمن أن **المستخدم المصرح له فقط** يصل لبياناته، ويوفر كذلك **مفتاحًا خلفيًا** للمستخدم المتقدم أو المطوّر للوصول لوظائف أعمق عند الحاجة. كل ذلك ضمن تصميم بسيط وأنيق لا يثير ارتباك المستخدم العادي ولا يشجعه على محاولة العبث.

## 12. شرح الأقسام الرئيسية في التطبيق

يقسم التطبيق إلى أربعة أقسام رئيسية، لكل منها واجهات ووظائف خاصة تخدم غرضًا محددًا. في هذه الفقرة نشرح بالتفصيل كل قسم على حدة وكيفية تفاعل المستخدم معه، مدعومًا بلقطات تخيلية للواجهة إن أمكن.

### 12.1 قسم العادات (Habit Tracker)

هذا القسم مخصص لمساعدة المستخدم على تتبع عاداتهم اليومية أو الدورية وتطوير روتين شخصي إيجابي. **واجهة القسم** تعرض قائمة بالعادات المسجلة. قد تأخذ شكل جدول ذو عمودين (اسم العادة – مستوى الإنجاز) أو بطاقات مرتبة شبكيًا. لكل عادة معروض: - اسمها مع أيقونة صغيرة دالة (مثال: 🏃‍♂️ للجري، 📖 للقراءة). - مؤشر تكرارها (مثل "يوميًا" أو "3/أسبوع"). - **معدل الإنجاز** الحالي أو سلسلة الأيام: مثلاً "🔥 5 أيام متتالية" أو "80% هذا الأسبوع".

ضمن واجهة العادات توجد **أزرار تفاعلية**: - زر **إضافة عادة جديدة** (+) والذي يفتح نموذج إدخال. في هذا النموذج، يحدد المستخدم اسم العادة، اختيار نوع التكرار (يومي/أيام معينة/أسبوعي)، وربما تحديد _هدف رقمي_ إن كان ينطبق (مثل "عدد صفحات" أو "مدة زمنية بالدقائق"). يمكن أيضًا ضبط وقت تذكير يومي للعادات المهمة. - بجانب كل عادة في القائمة، يوجد زر أو مربع اختيار **"إنجاز اليوم"**. عند نقره، يقوم التطبيق بتسجيل أن هذه العادة تم إنجازها لهذا اليوم. وقد يتغيّر شكل العنصر بعد التأشير (مثلاً يتخطط أو يصبح بلون success).

**التغذية الراجعة** فورية: إذا أشّر المستخدم عادة ما لهذا اليوم، يمكن أن يظهر بجانبها علامة (✔) وخلفية خضراء خفيفة تدل على النجاح. كما يتحدث مؤشر السلسلة أو النسبة ليعكس التحديث. على سبيل المثال، إذا كانت عادة "شرب الماء 8 أكواب" وتم التأشير اليوم، سيظهر بجانبها "اكتملت اليوم! 👍".

هناك إمكانية النقر على اسم العادة لفتح **تفاصيل العادة**. في شاشة التفاصيل تظهر: - رسم بياني صغير (Sparkline) يوضح أداء العادة في الأسبوع/الشهر الماضي (أيام منجزة مقابل غير منجزة). - مجموع الإنجازات: مثلاً "20 يوم من أصل 30 يوم" أو "نسبة الالتزام: 66%". - زر **تحرير** حيث يمكن تعديل اسم العادة أو هدفها أو وقت التذكير. - زر **حذف** العادة: مع تحذير "هل أنت متأكد؟ سيؤدي حذف هذه العادة إلى فقدان سجل إنجازاتها" – هذا يتطلب تأكيد المستخدم.

من ناحية الوظيفة، عند إضافة عادة جديدة، يقوم التطبيق بإدخال سجلها في جدول Habit، وربما يضف مباشرة سجل HabitLog لليوم الحالي (إذا أُنجزت فور الإضافة) أو ينتظر حتى يؤشر المستخدم. كل صباح جديد، يمكن للتطبيق إضافة تلقائي لسجلات عدم الإنجاز (أو ببساطة اعتبار عدم وجود سجل كعدم إنجاز). يمكن أيضًا للتطبيق تقديم _تلميح_ عندما يمر وقت طويل دون تأشير عادة: مثلاً إذا الساعة 10م ولم يتم تأشير "ممارسة الرياضة" اليوم، يظهر إشعار بأن اليوم شارف على الانتهاء ولم تُمارس عادتك – لتحفيز المستخدم.

**الارتباط مع الأقسام الأخرى**: قد يرتبط قسم العادات مع التقويم. إذا كانت عادة مجدولة في وقت معين (مثلاً عادة "الجري الساعة 6 صباحًا")، يمكن لتطبيق إضافة حدث يومي في التقويم أو على الأقل إطلاق تنبيه وقتها. لكن حتى بدون ذلك، إنجاز العادات اليومية يمكن أن يظهر في قسم الإحصائيات.

### 12.2 قسم الكورسات (Courses Manager)

هذا القسم يساعد المستخدم في تنظيم وإدارة تقدمه في الدورات التعليمية أو المشاريع التعليمية التي يتابعها. الواجهة قد تتخذ شكل قائمة عمودية حيث كل صف يمثل كورس، أو مربعات (Tiles) لكل كورس تعرض ملخصًا سريعًا: - اسم الكورس (مثلاً "تعلم لغة بايثون" أو "دورة إدارة مشاريع"). - **شريط تقدم** يوضح النسبة المئوية للإنجاز في هذا الكورس. إن كان الكورس 10 وحدات وتم إنجاز 4، يظهر الشريط 40%. - الحالة: يمكن تمييز الكورسات المكتملة بوضع علامة (مثل ✅ مكتمل) أو تلوين الشريط بلون مميز إذا وصل 100%. الكورسات النشطة تبقى بلون عادي، والمؤجلة/المتوقفة ربما بلون رمادي أو بتسمية "مؤجل".

في أعلى القسم قد توجد **أداة فلترة** بسيطة: مثلا قائمة منسدلة لاختيار عرض "نشطة فقط / مكتملة / الكل"، أو فرز حسب الأبجدية أو نسبة الإنجاز.

وظائف المستخدم الرئيسية هنا: - **إضافة كورس جديد**: بالنقر على زر "+" يفتح نموذج يطلب: اسم الكورس، وصف اختياري، عدد الدروس أو الوحدات إن معروف، وربما تاريخ البدء المتوقع أو موعد انتهاء مستهدف. بعد الحفظ، يظهر الكورس في القائمة. - **تحديث تقدم الكورس**: يمكن للمستخدم النقر على كورس معين فتظهر شاشة التفاصيل. في التفاصيل يوجد: - خانة لتعديل نسبة الإنجاز مباشرة (مثلاً إدخال رقم الدروس المكتملة فيحدّث النسبة). - أو أزرار مثل "تم إنجاز وحدة" لزيادة التقدم بمقدار معين. - عرض قائمة بالوحدات أو الدروس مع تأشير ما أُنجز (إن كنا نمثل الوحدات بشكل منفصل). - معلومات إضافية: مثل "تاريخ آخر نشاط: قبل 3 أيام"، "متوقع الإنهاء خلال أسبوعين بناءً على وتيرتك الحالية". - **تنظيم مواعيد الكورس**: قد يتكامل القسم مع التقويم. فمثلاً من شاشة تفاصيل الكورس، يمكن أن يكون هناك زر "أضف إلى التقويم" والذي يسمح بجدولة جلسات دراسة لهذا الكورس. على سبيل المثال، يختار المستخدم يومي الإثنين والأربعاء 7م-8م للدراسة، فينشئ التطبيق أحداث متكررة في التقويم. أو إذا عرف موعد امتحان أو مشروع متعلق بالكورس، يمكن إضافته كحدث في التقويم مع رابط للكورس.

**تشجيع المستخدم**: بمجرد إنجاز كورس (progress 100%)، يمكن للتطبيق تهنئة المستخدم (نافذة منبثقة "مبارك، أنهيت الدورة!") وربما يطلب تقييم التجربة أو تدوين ملاحظات التعلم. يحتفظ الكورس بحالته كمكتمل، ويمكن أن ينتقل إلى قسم فرعي "مكتمل" حتى لا يختلط مع الجاري.

**ربط البيانات**: حين يحدّث المستخدم تقدم الكورس، يتم تحديث السجل في قاعدة البيانات (حقل Progress). كما يمكن أن يُسجل تاريخ هذا الإنجاز (ربما في جدول لوحدات منجزة لو أردنا تحليل السرعة).

### 12.3 قسم التقويم (Calendar)

يعتبر القلب النابض لجدولة **المهام والأحداث**. الواجهة كما أسلفنا هي تقويم شبكي: - **طريقة العرض**: يمكن للمستخدم التبديل بين عرض شهري وشريط أجندة أسبوعي. الوضع الشهري يعرض الأسابيع والشهور التقليدية، بينما الأسبوعي يركز على تفاصيل السبع أيام بشكل جدول ساعات. - في العرض الشهري، كل تاريخ عبارة عن مربع يحتمل أن يحتوي قائمة قصيرة بالأحداث. إذا كان هناك أحداث عديدة قد يتم تمثيلها كنقاط أو لون معين مع رقم يشير لعدد الأحداث.

على جانب التقويم ربما تظهر **لوحة المهام اليومية**: عندما ينقر المستخدم على يوم معين، تعرض القائمة الجانبية تفاصيل مهام ذلك اليوم (عنوان المهمة، توقيتها، حالتها). هذا يسمح بسرعة التحرير: يمكن النقر على مهمة من القائمة لتعديلها أو وسمها كمكتملة.

**إضافة مهمة/حدث**: - عبر زر "+ حدث جديد" أو بالنقر المزدوج على تاريخ في التقويم. يفتح نافذة إدخال تحوي: عنوان، وصف اختياري، تاريخ (يكون محددًا مسبقًا باليوم المختار)، وقت البدء والانتهاء (اختياري للمهمات ذات الوقت المحدد، ويمكن تركه مفتوحًا للمهمات طيلة اليوم)، اختيار نوع الحدث (اجتماع، مهمة، مناسبة... أو ربطه بقسم كالعادات أو الكورسات). - يمكن أيضًا تحديد **أولوية** أو _تعيين تذكير_ (مثلاً تنبيه قبل الموعد ب15 دقيقة، أو في صباح ذلك اليوم). - بعد الحفظ، يقوم التطبيق بإضافة هذا الحدث لقاعدة البيانات (جدول CalendarEvent). سيظهر فورًا في واجهة التقويم (ربما على شكل مستطيل ملون في خانة اليوم، أو في قائمة اليوم الجانبية).

_شكل تخيلي: عرض التقويم الشهري مع أحداث موسومة بالألوان وعددها في كل يوم. يوم 7 مأخوذ كمثال حيث توجد مهمتان محددتان._

**تفاعل المستخدم مع المهام**: - يمكن سحب حدث وإفلاته إلى يوم آخر (في الواجهة) لتغيير تاريخه. هذا يستدعي تحديث حقل التاريخ في قاعدة البيانات. - يمكن تمديد حدث (إن كان له مدة) في العرض الأسبوعي بتغيير حجمه في المخطط الزمني. - عند النقر على حدث محدد، تظهر نافذة منبثقة صغيرة تعرض تفاصيله وخيارات مثل تحرير أو حذف. - لوسم المهمة كمكتملة، ربما يوجد مربع اختيار بجانب الحدث في قائمة اليوم. إن تم التأشير، يغير التطبيق حالة IsDone للحدث. ويمكن أن يظهر في التقويم بشكل مختلف (كأن يصبح العنوان مشطوبًا أو بلون باهت).

**التكامل مع الأقسام**: - المهام المتعلقة بالعادات: إذا اختار المستخدم عند إنشاء حدث أنه يمثل "عادة: قراءة 20 صفحة"، فيمكن للتطبيق أوتوماتيكيًا عند وسم هذا الحدث كمكتمل أن يسجل عادة القراءة كمنجزة ذلك اليوم في قسم العادات. هذا ربط منطقي مفيد. - المهام المتعلقة بالكورسات: على نحو مماثل، إذا أُنشئ حدث "أكمل درس 5 من دورة X" وحدد نوعه مرتبط بكورس X، عند التأشير كمكتمل، يمكن أن يزيد التطبيق تقدم الكورس بنسبة معينة. - أحداث متكررة: التقويم يدعم إنشاء حدث متكرر (يومي، أسبوعي، شهري). مثلاً "محاضرة كل يوم خميس الساعة 10 صباحًا". يتم تخزين هذا كحدث رئيسي مع نمط تكرار أو كأحداث منفصلة لكل occurrence. المعالجة حسب إمكانية SQLite، قد نبسطها بتوليد أحداث متكررة لبضعة أسابيع قادمة أو التعامل معها برمجياً.

**التنبيهات (Notifications)**: إذا كان التطبيق يعمل بالخلفية (أو المستخدم فتحه)، سيطلق **تنبيهًا** عندما يحين موعد حدث معين. مثلاً، في بداية اليوم قد يجمع جميع أحداث اليوم ويظهر إشعار "لديك 3 مهام اليوم". وعند اقتراب وقت محدد (مثلا اجتماع 2م)، يظهر تنبيه خاص قبلها. يمكن استخدام آلية Toast Notifications في Windows لذلك، أو إشعار ضمن التطبيق. هذه التنبيهات تتبع إعدادات المستخدم (يمكن كتمها أو تخصيص صوتها).

### 12.4 قسم الإحصائيات (Statistics & Reports)

هذا القسم يعرض **لوحات بيانية وتحليلات** حول بيانات المستخدم، مما يعطيه تصورًا ملموسًا عن أدائه ووقته. الواجهة تتكون من عدة عناصر رسومية: - **بطاقة/عنصر "نظرة عامة"**: تعرض بعض الأرقام الأساسية بشكل بارز – مثلاً "مهام أُنجزت هذا الأسبوع: 15 من 18 (83%)"، "معدل الالتزام بالعادات هذا الشهر: 75%". - **رسم بياني لعدد المهام المنجزة يوميًا**: رسم خطي أو عمودي يظهر الأيام (أو الأسابيع) على المحور الأفقي والعدد على المحور العمودي. يعطي فكرة عن أي الأيام أكثر إنتاجية. - **رسم بياني لسلسلة العادات**: قد يكون لكل عادة رسمها، أو تجميعي. مثلاً مخطط "سلسلة الإنجاز (Streak)" يعرض أطول سلسلة أيام متتالية لكل عادة في الفترة الماضية. - **مخطط دائري لتوزيع المهام حسب الفئة**: على سبيل المثال، يظهر نسبة المهام المتعلقة بالعمل، الدراسة، الصحة، ... وذلك إن كنا نصنف المهام. أو توزيع وقت المستخدم (نسبة الوقت المنقضي في الدراسة مقابل العمل مقابل الترفيه، إن توفرت بيانات كهذه). - **جدول ملخّص**: ربما قائمة بالعادات وما تحقق منها من أهداف شهرية، أو الكورسات ومتبقي الدروس/الأيام لإنهائها حسب الوتيرة.

هذه الإحصائيات يتم توليدها عند فتح القسم وربما **تُحدّث بلحظية** إذا قام المستخدم بأعمال تؤثر عليها. مثلا، لو أكمل المستخدم مهمة مؤشرة كمهمة عالية الأولوية، يمكن أن ينعكس ذلك مباشرة في مخطط الإنجاز اليومي.

يمكن للمستخدم **تخصيص نطاق زمني**: في أعلى القسم يوجد شريط لاختيار فترة التقرير (أسبوع حالي، أسبوع ماضٍ، شهر، سنة، أو فترة مخصصة). بناءً عليه تتحدث كافة المخططات.

أي **فجوات أو انخفاضات** يمكن أن تبرز بلون مختلف لجذب الانتباه. كمثال، عمود يوم لم تُنجز فيه أي مهمة قد يكون باللون الأحمر، أو عادة لم تحقق هدفها خلال الأسبوع تظهر علامة تحذير بجانبها.

التطبيق قد يوفر زر "تصدير التقرير" والذي يجمع هذه الإحصاءات في ملف PDF أو على الأقل يلتقط Screenshot لهذا القسمهذا مفيد لو أراد المستخدم حفظ نتائج شهر كامل أو مشاركتها.

من الجانب التقني، قسم الإحصائيات يستدعي وظائف عديدة لاحتساب المؤشرات من قاعدة البيانات: - حساب نسبة إنجاز المهام = المهام المكتملة / إجمالي المهام في المدة * 100%. - حساب معدل الالتزام بالعادة = (أيام الإنجاز / أيام المفترض) لكل عادة أو متوسطها. - إيجاد أكثر عادة مواظب عليها، وأكثر عادة متعثرة. - لو أردنا، نجد أيضًا "أكثر يوم مشغول" (اليوم الذي كان فيه أكبر عدد مهام). - إن توافرت بيانات زمنية (مثل مؤقت استعمال) يمكن إظهار "متوسط ساعات العمل اليومية".

هذه المعلومات ليست فقط للاستعراض بل **تغذي تحسينات سلوك المستخدم**. على سبيل المثال، إذا وجد القسم أن المستخدم يميل إلى إنجاز مهام أقل يوم الأحد، قد يضيف ملاحظة: "أيام الأحد لديك إنتاجية أقل. حاول توزيع المهام بحيث يخف الضغط هذا اليوم." أو لو عادة معينة دائماً غير مكتملة يمكن تمييزها واقتراح تعديلها.

أخيرًا، تصميم الرسوم البيانية اتبع مبادئ الوضوح: - عناوين وجُمل وصفية أعلى كل مخطط. - وسيلة تمييز اللون أو شكل الخط لكل سلسلة بيانية مع دليل (Legend). - استخدام وحدات مفهومة (عدد المهام، نسبة مئوية، ساعات...). - الحفاظ على اتساق الألوان مع بقية الواجهة (مثلا اللون الأساسي في الثيم يستخدم في الأعمدة أو الخطوط).

قسم الإحصائيات بهذا الشكل يمنح المستخدم **رؤى معمّقة** حول نشاطه، مما يحقق أحد أهداف المشروع في **رفع وعي المستخدم الذاتي** بوقته وإنجازه. الاطلاع المنتظم على هذه التقارير يمكن أن يحفّز المستخدم على التحسن المستمر (كونه يرى تقدمه بوضوح)، كما أشارت بعض الدراسات إلى أثر تتبع التقدم على الحالة المزاجية والتحفيز

## 13. التخصيص والإعدادات (Customization & Settings)

في هذا القسم يستطيع المستخدم **التحكم بالمظهر وسلوك التطبيق** لملاءمة تفضيلاته الشخصية وضمان أفضل تجربة استخدام. شاشة الإعدادات عادة ما تكون مقسمة إلى تبويبات أو مجموعات تنظيمية مثل: المظهر، التنبيهات، البيانات، وغيرها.

### تخصيص المظهر (Themes & Appearance):

يتيح التطبيق **تبديل السمة** (Theme) بين وضعين على الأقل: _وضع مضيء (Light Mode)_ افتراضي بخلفية فاتحة وخطوط داكنة، و_وضع مظلم (Dark Mode)_ بخلفية داكنة وخطوط فاتحة لإراحة العين ليلًا. التبديل يتم بزر أو مفتاح تبديل (Toggle) بسيط. عند تغييره، تطبق الألوان فوريًا على جميع النوافذ المفتوحة باستخدام موارد WPF كما تم الإعداد. أيضًا يمكن توفير خيارات ألوان رئيسية (Primary Color): مثلا يختار المستخدم لونًا مفضلاً (أزرق، أخضر، بنفسجي) فيستخدمه التطبيق لأشرطة العناوين وإبراز الأزرار.

**الخطوط وحجم النص**: بعض المستخدمين قد يفضلون تكبير الخط لتحسين القراءة. من خلال قائمة منسدلة أو منزلق يمكن اختيار حجم الخط العام (صغير - متوسط - كبير). التطبيق حينها يضاعف أحجام الخطوط النسبيّة. قد تشمل الإعدادات أيضًا نوع الخط (Font) إذا أراد المستخدم تخصيصه، لكن عادة نحصرها لضمان الاتساق (ربما خيار بين خط تقليدي وآخر عصري).

### إعدادات الإشعارات والتنبيهات:

يستطيع المستخدم تحديد أي تنبيهات يريد استقبالها: - **تفعيل/تعطيل إشعارات العادات**: كإرسال تذكير إن لم يؤشر عادة معينة بحلول وقت معين في اليوم. - **تنظيم مواعيد الإشعار**: مثلاً تذكير صباحي الساعة 8 بكل مهام اليوم، أو تذكير مسائي الساعة 9 لأي عادة متبقية. - **صوت الإشعار**: إن أراد المستخدم تغيير نغمة التنبيه أو كتم الصوت نهائيًا (يظل الإشعار بصريًا). - **إشعارات البريد الإلكتروني**: لو دعمنا مثلاً إرسال تقارير أسبوعية على بريد المستخدم، يمكنه تشغيلها أو إيقافها.

### إدارة البيانات:

بعض الخيارات المتعلقة ببيانات المستخدم: - **نسخ احتياطي يدوي**: زر "إنشاء نسخة احتياطية" يقوم بتصدير ملف (ربما SQLite DB نفسه أو JSON) يمكن حفظه خارجيًا. وبالمثل زر "استيراد نسخة احتياطية" لاستعادة البيانات. هذه الإجراءات تطلب تأكيدات لمنع الكتابة فوق البيانات الحالية عن طريق الخطأ. - **إعادة ضبط المصنع**: خيار خطير يكون مخفيًا عادة، ولكن يمكن وجوده (ربما مخصص تحت كلمة سر خاصة كما سبق). إذا اختاره المستخدم (بعد عدة تأكيدات)، يقوم التطبيق بمسح قاعدة البيانات الحالية وإعادة تهيئة التطبيق كأنه جديد. - **كلمة المرور**: خيار لتغيير كلمة مرور الدخول. يعرض نموذج إدخال الكلمة الحالية ثم الجديدة مرتين. إذا تم، يحدث التطبيق التخزين الآمن لها (تجزئة جديدة). ربما يقفل التطبيق ويطلب تسجيل الدخول من جديد لضمان الأمان.

### مزامنة وتكامل (إن وجدت):

على الرغم من أن التطبيق حاليًا غير مرتبط بسحابة، إلا أنه يمكن التخطيط لإعدادات مثل: - تسجيل الدخول بحساب (إن توسعنا ليصبح عبر حسابات متعددة). - مزامنة مع تقويم خارجي (كإدخال API key لتقويم Google). - ربط مع تطبيق هاتف (كإظهار QR code للمصادقة). هذه ليست ضمن نطاقنا الحالي، لكن مكانها الطبيعي سيكون في الإعدادات ضمن قسم "التكامل" أو "الحسابات".

### إعدادات متقدمة:

إذا كان "وضع المطور" مفعّلًا، قد يظهر تبويب إضافي في الإعدادات **خيارات مطور**: مثل عرض سجلات النظام، تشغيل وضع debug، السماح باستيراد بيانات تجريبية...إلخ. هذه لن تظهر للمستخدم العادي.

### تجربة استخدام الإعدادات:

تم الحرص على جعل شاشة الإعدادات منظمة. قائمة جانبية ربما تعرض فئات الإعدادات، وعند النقر على فئة تظهر الخيارات في الجهة اليمنى مع وصف مختصر تحت كل خيار لمساعدة المستخدم على فهم تأثيره. التغييرات في الإعدادات **تأخذ مفعولها فورًا** (مثل theme, font) أو عند إعادة تشغيل التطبيق لبعض الخيارات (مثل تغيير كلمة المرور - المرة القادمة سيستخدم الجديدة). ينبغي توضيح ذلك للمستخدم (مثلاً رسالة "سريان عند إعادة التشغيل").

عند تغيير أمور خطرة (كلمة مرور، إعادة ضبط)، يتم طلب تأكيد. وحتى تأكيد تغيير بسيط مثل theme ليس سيئًا: يمكن عرض معاينة مباشرة، أو عبارة "يمكنك إعادة اللون الافتراضي من هنا...".

### حفظ وتخزين الإعدادات:

يتم حفظ الإعدادات إما في ملف إعدادات config أو في قاعدة البيانات (جدول UserSettings). في SQLite، يمكن استخدام جدول Key-Value لحفظ مثل هذه الأمور: مثال (SettingName, SettingValue). أو استخدام Properties.Settings الخاص بـ .NET والذي يخزن في AppData. كلاهما مناسب. المهم أن أي تغيير يقوم به المستخدم يستمر بعد إغلاق التطبيق (Persisted).

### أهمية التخصيص:

خاصية التخصيص تزيد من **رضا المستخدم** عن التطبيق، لأنه يستطيع ملاءمته لراحته. مثلاً المستخدم الذي يعمل ليلاً سيحب الوضع المظلم. المستخدم الكبير سنًا سيقدر تكبير الخط. وإدارة التنبيهات تضمن أن المستخدم لا ينزعج من إشعارات كثيرة أو على العكس يحصل على التنبيه الذي يريده. كذلك إعدادات الأمان (تغيير كلمة المرور) تعطيه الثقة بقدرته على التحكم بحماية بياناته دون تعقيد.

باختصار، قسم الإعدادات والتحكم بالتخصيص مصمم ليكون شاملًا لكنه مبسط قدر الإمكان. يوضع كله في مكان واحد واضح لكي يعتاد المستخدم أنّ أي تغيير في سلوك التطبيق مكانه الإعدادات. هذا يتوافق مع أفضل الممارسات لتطبيقات الإنتاجية الحديثة (حيث عادة هناك شاشة إعدادات مركزية بدلاً من توزيع الخيارات هنا وهناك).

## 14. تحليل تجربة المستخدم (UX/UI Analysis)

تجربة المستخدم في هذا التطبيق تم تصميمها بحيث تكون **سلسة وبديهية ومحفزة**. سنناقش هنا بعض جوانب UX/UI التي تم التركيز عليها لضمان رضى المستخدم، استنادًا إلى مبادئ _قابلية الاستخدام_ وأبحاث تصميم واجهات تطبيقات الإنتاجية.

### البساطة وتقليل التعقيد:

رغم تنوع وظائف التطبيق (مهام، عادات، كورسات، تقويم)، تم الحرص على عدم جعل الواجهة مزدحمة. اتبعنا مبدأ "**Minimalist Design**" بعرض العناصر الأساسية فقط في أي شاشة، ووضع الخيارات المتقدمة في أماكن يمكن إظهارها عند الطلب (مثل زر "المزيد ..." أو في صفحات منفصلة). على سبيل المثال، في قسم العادات، رؤية المستخدم الرئيسية هي أسماء العادات وزر تأشير الإنجاز – بقية التفاصيل (إحصاءات العادة، إعدادات التذكير) مخفية في شاشة التفاصيل الخاصة بتلك العادة. هذا التقسيم يمنع _تكدس المعلومات_ التي قد تربك المستخدم.

### التناسق (Consistency):

جميع أجزاء التطبيق تتبع **قواعد تصميم موحدة**. الألوان والأيقونات تتكرر بمعاني ثابتة: اللون الأخضر يدل على إنجاز أو حالة جيدة (إنجاز مهام، عادات مكتملة)، اللون البرتقالي/الأصفر للتنبيهات (مهام قريبة موعدها)، الأحمر للإخفاقات أو المتأخرات. أما الأزرق مثلا كلون أساسي للأزرار فهو اللون التفاعلي العام. هذا التناسق يساعد المستخدم ضمنياً على تعلم استخدام التطبيق بسرعة – إذا فهم أن إشارة ✅ تظهر عند إكمال أي شيء، سيعرف بمجرد رؤية تلك الإشارة في قسم آخر أنها تعني الاكتمال هناك أيضًا.

كذلك نمط التفاعل ثابت: الضغط المزدوج للتحرير، الضغط الطويل (إن دعمناه) لفتح قائمة خيارات، رمز القلم ✎ للتحرير، سلة المهملات 🗑️ للحذف، إلخ. باستخدام _أيقونات قياسية_ من المكتبات المعروفة (Material Design Icons مثلاً)، استفدنا من ألفة المستخدمين المسبقة (تكون توقعاتهم صحيحة حول وظيفة كل زر).

### توفير الإرشاد والمساعدة الفورية:

تم تضمين _عناصر مساعدة_ في الواجهة بشكل غير مزعج. مثلاً، عند فتح التطبيق لأول مرة يمكن عرض **جولة تفاعلية** (Tour) تمرّ على الأقسام وتشرح للمستخدم باختصار ماذا يفعل كل قسم (قد تكون على شكل نوافذ Tooltips تبرز حول العناصر الرئيسية مع شرح). أيضًا، رمز "?" في زاوية كل شاشة يؤدي إلى عرض _دليل مختصر_ للاستخدام – هذه مثلاً صفحة صغيرة تذكر اختصارات لوحة المفاتيح المتاحة، أو ماذا يعني كل لون في الإحصائيات.

داخل التطبيق، عند حصول أخطاء أو غموض، **رسائل خطأ/تنبيه واضحة** توجه المستخدم. مثال: لو حاول إضافة كورس بنفس اسم كورس موجود، نظهر رسالة "هذا الكورس موجود بالفعل. يمكنك تغيير الاسم أو تفقد قائمة الكورسات." بدلاً من رسالة تقنية جافة. هذا يتماشى مع مبدأ كتابة رسائل المستخدم بلغة مفهومة ومن دون لوم، بل تقدم حلًا.

### الاستجابة والتفاعلية:

التطبيق **سريع الاستجابة**: التنقل بين التبويبات فوري تقريبًا (حيث نحمل البيانات مسبقًا عند الدخول لتسريع ذلك). أزرار الضغط تعطي **مؤشرًا** بنقرها (تغيير لون خفيف) كي يعلم المستخدم أنه تم الضغط بنجاح. عند تنفيذ عمليات تستغرق وقتًا (كحساب إحصائيات لفترة طويلة مثلاً)، يظهر مؤشر انتظار (Loading spinner) صغير لمنع إحساس المستخدم بأن التطبيق تجمد.

أي تفاعلات دراغ ودروب (مثل إعادة جدولة مهمة) تعالج بصريًا – ترى المهمة تتحرك مع المؤشر، وعند الإفلات تتلقى تأكيدًا (مثل مهمة يوم 5 يوليو انتقلت إلى 6 يوليو، فيُبرز المربع الجديد للحظة). هذه التفاصيل الدقيقة ترفع إحساس التحكم (User Control) والرضا.

### الاعتناء بحالات الأطراف (Edge Cases):

فكرنا في تجربة المستخدم في الحالات غير الاعتيادية أيضًا: - **عند خلو البيانات**: عندما لا يكون لدى المستخدم أي مهمة أو عادة مدخلة (مثلاً أول استخدام)، بدلاً من شاشة فارغة، نعرض رسالة ودية: "لم تقم بإضافة أي عادة بعد. اضغط (+) في الأعلى لإضافة عادتك الأولى!" وربما رسم توضيحي بسيط. هذا يشجع المستخدم على بدء الاستخدام بدل أن يشعر بالضياع أمام شاشة خالية. - **عند اكتمال كل شيء**: إذا أنجز المستخدم كل مهامه لليوم، يمكن أن يظهر رسم تعبيري سعيد مع عبارة "أنجزت كافة مهام اليوم! أحسنت." أو مثلاً يتغير لون خلفية التطبيق بشيء احتفالي بسيط للحظات. هذه _التغذية الراجعة الإيجابية_ لها أثر تحفيزي مهم. - **التعامل مع الأخطاء**: إذا فشل حفظ بيانات لسبب ما (مثلاً مشكلة بالقرص أو قاعدة البيانات مقفلة)، تظهر رسالة واضحة وتقترح حلاً ("تعذّر حفظ التغييرات. حاول مجددًا وإذا استمر الخطأ أعد تشغيل التطبيق."). محاولة منع هذه الأخطاء أولى، لكن وجود خطة للتعامل معها مهم لتجنب إحباط المستخدم وفقدان ثقته.

### التحفيز والاستمرارية:

عنصر **الألعابية (Gamification)** تم توظيفه باعتدال لتحفيز المستخدم دون إلهائه. أمثلة: - شارات وإنجازات: عند الوصول لسلسلة 7 أيام عادة، يظهر في قسم العادات شارة "سبعة على التوالي!" أو وسام صغير بجانب العادة. كذلك إنهاء 5 مهام في يوم يعطي شارة يوم منتج. - مستوى الإنتاجية: يمكن حساب نقاط على كل إنجاز (مثلاً 10 نقاط لكل مهمة، 5 لكل عادة) وعرض "مستواك: 3 – منجز نشيط" كشكل من التصنيف. هذه الأمور مستوحاة من تطبيقات مثل Habitica (الذي يحوّل الحياة للعبة) ولكن هنا أبقيناها **اختيارية وهادئة** وليست لعبة كاملة. الهدف إعطاء دفعة حماس دون تشتيت عن المهمة الأساسية (الإنتاجية).

### إمكانية الوصول (Accessibility):

نظرنا أيضًا لمبادئ وصول ذوي الاحتياجات الخاصة: - التطبيق يدعم **التنقل بلوحة المفاتيح** بالكامل (Focus واضح على الأزرار، واختصارات مثل Alt+1 للانتقال للقسم الأول، إلخ). - دعم قارئ الشاشة: وضعنا أسماء بديلة مناسبة للأيقونات (AutomationProperties.Name) لكي يتمكن _قارئ الشاشة_ للمكفوفين من وصف كل عنصر ("زر إضافة مهمة"، "قائمة العادات تحتوي على 3 عناصر" ...). - تباين الألوان: اخترنا لوحة ألوان ذات تباين جيد بين النص والخلفية. وحتى في الوضع المظلم، النص الفاتح على الخلفية الداكنة يحقق نسبة تباين عالية لتسهيل القراءة. - الحجم الديناميكي: كما ذكرنا، خيار تكبير الخط مفيد، وكذلك الواجهة تستجيب جزئيًا إذا زاد المستخدم حجم النص في نظام التشغيل.

### تقييم قابلية الاستخدام:

لو أجرينا اختبار قابلية استخدام (Usability Testing) مع مستخدمين، سنراقب مدى سهولة إنجازهم لمهام مثل "سجل عادة جديدة" أو "أضف حدثًا في التقويم". نتوقع معدلات نجاح عالية، حيث صممنا التدفق ليكون مختصرًا: عادة جديدة تتطلب 3 حقول فقط ثم حفظ، حدث جديد مباشر جدًا. أيضًا زمن إكمال المهام (Task Completion Time) ينبغي أن يكون قصيرًا (أقل من دقيقة لإدخال مهمة عادية مثلاً). أي عقبات يظهرها الاختبار ستوجه تحسينات مستقبلية (كأن نجد بعض المستخدمين لم يلحظ زر الإضافة لأن لونه هادئ جدًا - فنقوم بتعديله ليبرز أكثر).

خلاصة تحليل UX/UI: التجربة تهدف لجعل التطبيق **مساعدًا ذكيًا** وليس مجرد أداة جامدة. أي أن المستخدم يشعر أن التطبيق _يفهم احتياجاته_ (من خلال التذكيرات الذكية، والإحصاءات الداعمة، والتخصيص) ويقدم له حلولًا بدون عناء. كما حرصنا على تقليل أي إحباط أو التباس عبر واجهة نظيفة وموجهات واضحة. ومن خلال الالتزام بمبادئ تصميم راسخة (كإرشادات Nielsen لعناصر واجهة الاستخدام وحل الأخطاء بسرعة، إلخ)، نطمئن أن التطبيق سيقدم تجربة محترفة تضاهي التطبيقات التجارية المعروفة في مجال الإنتاجية.

## 15. المقارنة مع حلول بديلة

يوجد في السوق العديد من التطبيقات والأدوات التي تغطي أجزاءً من وظائف هذا النظام. سنستعرض ثلاثة أمثلة شائعة (Notion، Trello، Habitica) ونقارنها باختصار مع نظامنا من حيث **نطاق الوظائف** والفئة المستهدفة وطريقة الاستخدام، لإبراز التمايز والفائدة التي يقدمها مشروعنا.

- **Notion (نوشن)**: منصة مرنة للغاية تعتبر كلوحة فارغة يمكن للمستخدم بناء أي شيء عليها من ملاحظات وقوائم مهام وقواعد بيانات بسيطة. تتميز Notion بقدرة تخصيص هائلة؛ يمكن للمستخدم تصميم صفحة لتتبع العادات أو المهام بالطريقة التي يريد لكنها _ليست متخصصة_ بشكل جاهز لإدارة العادات أو الحياة الشخصية، بل تتطلب من المستخدم إنشاء جداول وربطها بنفسه. قد يسبب هذا **إرباكًا للمستخدم العادي** أو تطلب وقتًا لضبط الإعدادات المناسبة. في المقابل، تطبيقنا _جاهز ومختص_: يقدم واجهات مهيأة لتتبع العادات والمهام دون حاجة لبناء أي شيء من الصفر. Notion أيضًا يعتمد على الإنترنت للتخزين السحابي (رغم وجود وضع أوفلاين جزئي) بينما تطبيقنا محلي بالكامل. من جهة أخرى، Notion يوفر ميزات لا نقدمها مثل التعاون الجماعي والتدوين الحر؛ لكن المستخدم الفردي الذي يحتاج _حلاً شخصيًا بسيطًا_ قد يفضل نظامنا لأنه أخف وأسرع تعلمًا. **خلاصة**: Notion قوي ومرن جدًا لكن مرونته هي سلاح ذو حدين لأنها تحتاج ضبط. تطبيقنا محدود بنطاقه ولكنه مباشر ويلبي الحاجة المحددة دون تعقيد.
- **Trello (تريللو)**: تطبيق شهير لإدارة المهام باستخدام لوحات كانبان (بطاقات في أعمدة). Trello ممتاز لتنظيم مهام المشاريع والعمل التعاوني، حيث يسهل تحريك البطاقات بين أعمدة _قيد العمل_ و_منجز_ وغيرها. ومع أنه يمكن استخدامه بشكل شخصي، إلا أن Trello لا يحتوي بشكل أصلي على مكونات لتتبع عادات يومية أو لعرض تقويم تفاعلي مفصل (إلا بإضافات Power-Ups). Trello يركز على **المهام القصيرة المدى** في سياق مشروع أو قائمة _To-Do_ مستمرة، ولا يوفر تحليل إحصائي عميق أو تتبع أداء زمني طويل بدون أدوات خارجية. بالمقارنة، تطبيقنا يدمج _التقويم والعادات والإحصاءات_ في مكان واحد – ميزة غير موجودة في Trello مباشرة. Trello قوي في عرض العمل تقدمًا (workflow) لكنه قد يفتقر لتحفيز بناء العادات أو حساب المعدلات. أيضًا Trello منصة ويب بشكل أساسي (مع تطبيقات للموبايل)، بينما تطبيقنا سطح مكتب أوفلاين. **خلاصة**: إذا كان المستخدم هدفه فقط قوائم مهام بسيطة متجددة دون اهتمام بالعادات أو تحليل الأداء، قد يفي Trello بالغرض. لكن لمستخدم يريد إدارة _جوانب حياته الشخصية المتنوعة_ مع التحليلات، نظامنا يقدم حلاً أشمل مصمم لهذا الغرض.
- **Habitica (هابيتيكا)**: تطبيق فريد من نوعه يجعل تتبع العادات والمهام في شكل **لعبة تقمص أدوار (RPG)** عندما ينشئ المستخدم مهامًا وعادات ويكملها، يحصل على نقاط خبرة وقطع ذهبية لترقية شخصيته الخيالية داخل اللعبة. هذا النهج ممتع لمن يحب التلعيب (Gamification) وقد أثبت فعاليته لبعض الأشخاص في التحفيز. Habitica ممتاز لبناء العادات تحديدًا، ويشمل مجتمعًا وتحديات جماعية أيضًا. ومع ذلك، قد لا يناسب جميع المستخدمين – البعض قد لا يهمه جانب اللعبة ويريده أداة جادة مباشرة. أيضًا Habitica _أقل شمولاً في إدارة مشاريع أو كورسات_: فهو يركز على مهام قصيرة وعادات ويمكن قولبة بعض المهام طويلة الأمد فيه، لكن لا يوجد فيه تقويم تقليدي أو تقسيم لمجالات الحياة بشكل منفصل (هو يصنف المهام لعادات ويومي وأسبوعي...). نظامنا يتشابه مع Habitica في فكرة تتبع العادات والتحفيز، لكننا أقل _لعبًا_ وأكثر **تحليلية**: لدينا رسوم بيانية وتقارير أداء، بينما Habitica التحفيز فيه يأتي من التقدم في اللعبة وليس من استعراض البيانات. أيضًا Habitica منصة على الويب/جوال ويتطلب اتصالاً (مع وجود بعض ميزات أوفلاين محدودة)، أما نظامنا فهو _خاص وشخصي بالكامل_. **خلاصة**: Habitica حل رائع لمن يتفاعل مع الألعاب بشكل إيجابي ويريد جعل الإنتاجية لعبة جماعية ممتعة. نظامنا يخدم من يرغب بتطبيق تقليدي أكثر، مع تركيز على البيانات الشخصية والتحليل وربما الخصوصية (عدم الاعتماد على خادم خارجي أو مجتمع).

بالإضافة لهذه: - هناك تطبيقات مثل **Google Calendar** أو **Microsoft To Do** تقدم أجزاء مما نقوم به (تقويم ممتاز، وقوائم مهام متناثرة). لكن عادةً يحتاج الشخص لعدة تطبيقات (واحد للمهام، آخر للعادات مثل HabitBull، آخر للمذاكرة). ميزة نظامنا هي دمج المهمات المتنوعة في مكان واحد، مما يوفر وقت المستخدم ويعطيه **صورة كلية** لا توفرها التطبيقات المنفصلة. كما أن امتلاك البيانات محليًا ميزة لبعض المستخدمين الحريصين على الخصوصية أو الذين يعملون دون اتصال دائم.

·       مقارنة **التكلفة**: التطبيقات المذكورة أغلبها تعمل بخطط Freemium (مجاني مع حدود أو اشتراك مدفوع لمزايا إضافية). مشروعنا كمشروع شخصي هو مجاني للمستخدم (على فرض توزيعه كأداة مفتوحة أو مرفق بالبحث). هذا قد يكون نقطة إيجابية إن فكرنا فيه كمنتج للآخرين.

باختصار، كل من الحلول البديلة قوي في مجاله: Notion في المرونة الشاملة، Trello في إدارة مهام المشاريع بشكل بصري، Habitica في تحفيز العادات باللعب. لكن _نقطة التميز_ في نظامنا هي **التركيز والتكامل** للفرد: صمم خصيصًا ليلائم شخصًا يريد تطوير نفسه ومتابعة مهامه وعاداته دون عناء التخصيص الكثير أو تشتت التطبيقات المتعددة. إنه يحاول أخذ أفضل ما في الأفكار المختلفة: مرونة كافية (ليس صارمًا جدًا)، تنظيم بصري لطيف للمهام (تقويم)، وتحفيز وإحصاءات للعادات (مثل Habitica لكن بأسلوب جاد). وبالتالي يمكن أن يكون خيارًا جذابًا لمن لم يجد ضالته كاملة في أداة واحدة ويريد **الحل الوسط المتوازن** بين العمل واللعب والتحليل.

## 16. خطة التنفيذ (Development Plan)

لضمان بناء هذا المشروع بطريقة منظمة وفي إطار زمني محدد، تم وضع خطة تنفيذية مجدولة تتضمن **مراحل التطوير الأساسية** والمعالم الرئيسية (Milestones). هذه الخطة تساعد فريق العمل على تتبع التقدم كما تمكن أصحاب المصلحة من فهم متى سيتم إنجاز كل جزء. فيما يلي تصور لخطة التنفيذ على امتداد ربما **12 أسبوعًا** (حوالي 3 أشهر)، يمكن تعديلها بحسب الموارد المتاحة:

·       **المرحلة 1: جمع المتطلبات والتصميم (الأسبوع 1-2)**

·       إنجاز وثيقة SRS مفصلة (والتي هذا التقرير جزء منها) ومراجعتها مع فريق التطوير أو المشرف.

·       رسم نماذج أولية لواجهات المستخدم (Wireframes) لكل الأقسام الأساسية، وربما إنشاء تصميمات UI عالية الدقة باستخدام أدوات مثل Figma للتأكد من الشكل النهائي قبل التنفيذ.

- وضع تصميم قاعدة البيانات النهائي بناءً على التحليل (ERD) والتأكد أنه يغطي كل المتطلبات.  
    _مخرجات المرحلة:_ موافقة على التصميم UI/UX وقاعدة البيانات، وخطة تقنية واضحة (اختيار استخدام MVVM toolkit إن وجد، المكتبات اللازمة).
- **المرحلة 2: إعداد بيئة التطوير وبناء الهيكل العام (الأسبوع 3)**

·       إنشاء مشروع WPF جديد بهيكلية المجلدات (فصل ViewModels، Models، Views، Services).

·       إعداد قاعدة البيانات SQLite ووضع سكريبت لإنشاء الجداول.

·       تكوين الإطار العام للتطبيق: نافذة رئيسية، نافذة تسجيل الدخول، تطبيق الثيم الأساسي.

- تنفيذ نماذج البيانات (Classes for Habit, Course, etc) وربطها بالـORM أو مكتبة SQLite.  
    _مخرجات:_ تشغيل تطبيق هيكلي يسمح بتسجيل الدخول (ربما بكلمة مرور افتراضية ثابتة)، ثم رؤية نافذة رئيسية فارغة.
- **المرحلة 3: تطوير وظائف أساسية لكل قسم (الأسبوع 4-7)**  
    سيتم العمل على الأقسام بالتوازي أو تتابعيًا حسب عدد المطورين:

·       **الأسبوع 4:** قسم العادات – إنشاء واجهة القائمة، ربطها ببيانات تجريبية، ثم ربط بإضافة العادة وحفظها في DB، وظيفة تسجيل الإنجاز اليومي. التأكد من تحديث السلسلة والحسابات الأساسية.

·       **الأسبوع 5:** قسم الكورسات – تطوير واجهة القائمة، إضافة الكورس وتعديل التقدم، والتخزين.

·       **الأسبوع 6:** قسم التقويم – استخدام عنصر Calendar (أو بناء واحد) لعرض الأيام. تمكين إضافة حدث، تعديل الحدث، وحفظها في DB. ربط بعض المهام مع العادات/الكورسات إن أمكن.

- **الأسبوع 7:** قسم الإحصائيات – توليد بعض المخططات الأساسية باستخدام مكتبة رسوميات؛ مبدئيًا يمكن الاكتفاء بإحصاءات بسيطة (عدد المهام في الأسبوع والعادات) للتأكد من عمل الرسم البياني.  
    _مخرجات:_ تطبيق قابل للاستخدام بمزايا CRUD (إنشاء/تعديل/حذف) للعادات والكورسات والأحداث، ويعرض بيانات فعلية في التقويم وبعض الرسوم.
- **المرحلة 4: التكامل وتحسين المزايا (الأسبوع 8-9)**

·       ربط الأجزاء معًا: مثلاً جعل إنجاز العادة يظهر كتأثير في الإحصائيات، إضافة مهمة مرتبطة بكورس تزيد تقدم الكورس، ...

·       تنفيذ التنبيهات Notifications (آلية بسيطة باستخدام Toast إذا أمكن، أو نافذة إشعار داخلية).

·       إضافة _الكلمات السرية المخفية_ ووضع المطور: كتابة الشيفرة في LoginViewModel وAuthService للتعرف على كلمات خاصة. وإظهار عناصر devMode في الواجهة الرئيسية عند التفعيل (مثلاً علامة مائية "Dev").

- تحسين الـUX: إضافة اختصارات لوحة المفاتيح، drag & drop للأحداث، تأكيدات قبل الحذف، الخ.  
    _مخرجات:_ جميع الميزات الأساسية تعمل بانسجام مع تجربة مستخدم لائقة. في هذه النقطة يمكن تطبيق استخدامه بشكل شبه كامل.
- **المرحلة 5: الاختبار وضمان الجودة (الأسبوع 10)**

·       إجراء **اختبارات وظيفية** manual: التأكد من كل متطلبات المرحلة 4 تعمل (على سيناريوهات مثل "أضف 3 عادات وسجل 5 أيام... تحقق الإحصائيات الصحيحة").

·       تنفيذ **اختبارات وحدة** لبعض المكونات الحرجة: مثلاً AuthService (التحقق من كلمات المرور بما فيها الخاصة)، دوال حساب الإحصاء.

·       اختبار **قابلية الاستخدام**: الطلب من عدد قليل من المستخدمين (زملاء أو أصدقاء) تجربة التطبيق دون شرح مفصّل وتسجيل ملاحظاتهم: هل واجهوا صعوبة في فهم شيء؟ هل هناك أخطاء ظهرت؟

- اختبار **الأمان**: محاولة إدخال مدخلات غريبة (نص طويل جدًا في أسماء، حروف خاصة) للتأكد أن التطبيق يتعامل معها بأمان. وربما محاولة الوصول لملف DB بدون صلاحية لمعرفة تأثير التشفير إن طبقناه.  
    _مخرجات:_ تقرير اختبار يسرد المشاكل (Bugs) المكتشفة.
- **المرحلة 6: تصحيح الأخطاء والتحسين النهائي (الأسبوع 11)**

·       إصلاح كافة الأخطاء ذات الأولوية العالية المكتشفة في الاختبارات.

·       تحسينات نهائية على الواجهة بحسب ملاحظات المستخدمين التجريبيين (مثلاً توضيح أيقونة معينة، أو إضافة شرح هنا أو هناك).

·       تحسين الأداء إذا لوحظت بطيئات (مثل تحسين استعلامات قاعدة البيانات أو تحميل الصفحات الكبيرة).

- كتابة **دليل مستخدم مختصر** (صفحة "Help") ضمن التطبيق أو كملف PDF مرافق.  
    _مخرجات:_ نسخة مرشّح لإصدار (Release Candidate) من التطبيق.
- **المرحلة 7: التسليم والإطلاق (الأسبوع 12)**

·       تجهيز حزمة التثبيت (Installer) أو نشر التطبيق بطريقة ملائمة (قد يكون ClickOnce أو MSI) لتسهيل تثبيته من قبل المستخدم النهائي.

·       إجراء اختبار تثبيت على جهاز نظيف لضمان عدم وجود تبعيات ناقصة.

·       إعداد مواد العرض أو التقرير النهائي (مثلاً عرض بوربوينت إن كان مطلوبًا لشرح المشروع).

·       تسليم المشروع للمستخدمين المستهدفين أو للجنة التسليم.  
_مخرجات:_ التطبيق في صورته النهائية متاح للاستخدام، مع وثائق التسليم.

طبعا **إدارة المشروع** خلال هذه المراحل ستعتمد منهجية مرنة (Agile) بقدر الإمكان نظرًا لإمكانية ظهور تغييرات أثناء التطوير. قد نستخدم أدوات كنظام **Kanban** لتتبع المهام (بطاقات لكل ميزة/علة) وتقسيمها على الأسابيع. مع كل معلم (نهاية كل مرحلة)، سنقيم التقدم ونضبط الخطة لو لزم (مثلاً ربما نحتاج أسبوع إضافي للإصلاح إن ظهرت مشاكل جذرية).

كذلك يجب تضمين _فحوصات ما بعد الإطلاق_: مراقبة أداء التطبيق بعد الاستخدام الفعلي، وجمع آراء لتحسينات مستقبلية. هذه ستكون ضمن خطة الصيانة، لكنها تخرج عن نطاق "الإطلاق الأولي" المخطط هنا.

باختصار، الخطة الزمنية تضمن تغطية جميع خطوات التطوير من التصميم حتى الاختبار والتسليم، مع ترتيب منطقي للبناء. إنها تعطي أولوية لبناء المزايا الجوهرية أولاً، ثم التكامل، ثم المزايا الثانوية والتحسينات، لضمان أنه لو حدث تأخير، تكون الوظائف الأساسية على الأقل مكتملة ويمكن عرضها.

## 17. المخاطر المحتملة والحلول

لا يخلو أي مشروع برمجي من **المخاطر** التي قد تؤثر على جدوله الزمني أو جودته. من خلال خبرتنا وتوقعاتنا، حددنا عددًا من المخاطر المحتملة في هذا المشروع، ونقترح لكل منها **استراتيجيات للتخفيف** أو الحلول في حال وقوعها. يعد وعي الفريق بهذه المخاطر جزءًا مهمًا من عملية إدارة المشروع

### المخاطر التقنية (Technical Risks):

·       **عدم الإلمام الكافي بـWPF أو MVVM**: قد يواجه المطورون صعوبة إذا لم يكن لديهم خبرة عميقة في تطبيقات WPF وأنماط MVVM، مما قد يؤدي إلى تباطؤ التطوير أو أخطاء تصميمية. _خطة التخفيف_: تخصيص وقت في البداية للتدريب العملي أو دراسة أمثلة مشابهة. يمكن أيضًا الاعتماد على أطر MVVM جاهزة مثل Prism أو MVVM Light لتسهيل بعض الجوانب. وفي حال ظهرت مشكلة تقنية معقدة أثناء التطوير (كصعوبة في ربط التقويم بالبيانات)، يمكن طلب استشارة من خبير أو إعادة تقييم تصميم تلك الجزئية بشكل أبسط.

·       **تعقيدات التزامن مع قاعدة البيانات**: بما أن SQLite قاعدة بيانات ملفية، قد تحدث مشاكل في حال حاول التطبيق إجراء عمليات تزامنية مكثفة (مثلاً كتابة متزامنة كثيرة). _تخفيف_: استخدام نهج معاملات (transactions) بحكمة، والتأكد من إغلاق الاتصالات بقاعدة البيانات فور إنهاء العمليات. أيضًا إجراء اختبارات تحميل (إدخال مئات المهام والعادات) لرؤية الأداء وضبطه. إذا ظهرت اختناقات، أحد الحلول الممكنة استخدام **تخزين مؤقت في الذاكرة** (كashing) ثم الحفظ على فترات.

·       **التكامل مع مكتبات خارجية**: استخدام مكتبة الرسوم البيانية أو الإشعارات قد ينتج عنه مشكلات (عدم التوافق، ثغرات). _تخفيف_: اختيار مكتبات مستقرة ومجربة، قراءة التوثيق والأخذ بعين الاعتبار حدودها. وفي حال ظهرت مشكلة غير محلولة من المكتبة (مثل عيب في الرسم)، لدينا بدائل احتياطية (تغيير المكتبة أو تطوير رسم بسيط مخصص).

·       **مخاطر أمنية برمجية**: احتمال اكتشاف ثغرة تسمح بتجاوز شاشة الدخول أو وصول غير مصرح به للبيانات. _تخفيف_: إجراء مراجعة كود مركزة على نقاط الدخول (شاشة الدخول، تعامل كلمة السر) للتأكد من سد الثغرات الشائعة. مثلاً التأكد من عدم تخزين كلمة المرور في الذاكرة بشكل نصي بعد استعمالها، والتأكد من كلمات السر الخاصة معروفة فقط لفريق التطوير. إذا اكتشفت ثغرة أثناء الاختبارات، تعالج فوريًا وقد يؤجل الإطلاق لحين إصلاحها لأن الأمان أولوية.

### مخاطر المشروع (Project Risks):

·       **انحراف الجدول الزمني (Schedule Slippage)**: ربما يستغرق تنفيذ بعض الميزات أطول مما هو مخطط (كأن يتعثر قسم التقويم ويستهلك أسبوعين بدلًا من واحد). _تخفيف_: استخدام منهجية **Agile** مع تقدم تدريجي (مثلًا تطوير MVP لكل قسم بسرعة، ثم تحسينه) لكي يكون لدينا نسخة عملانية مبكرًا يمكن عرضها حتى لو لم تكن كاملة بنسبة 100%. أيضًا تصنيف المهام حسب الأولوية – لو ضاق الوقت، نضمن اكتمال الأساسي (مثلا الإحصائيات المتقدمة يمكن تأجيلها إن لزم). في حال حدوث تأخير فعلي، التواصل مع الجهات المعنية (المشرف أو العميل) مبكرًا لبحث إعادة ترتيب الأولويات أو تمديد جزئي.

·       **تغيّر المتطلبات أو ظهور طلبات جديدة**: قد يأتي في منتصف المشروع رغبة في إضافة ميزة غير مخططة (مثلاً "أريد مزامنة مع الهاتف الآن" أو "إضافة دعم متعدد المستخدمين"). هذا تغيير قد يهدد الجدول والجودة. _تخفيف_: الالتزام بوثيقة المتطلبات كما تم الاتفاق عليها، واستخدام **عملية ضبط التغيير**: أي نقيم التغيير، إن كان جوهريًا نؤجل إلى نسخة لاحقة، أو إن كان بسيطًا نحاول دمجه دون تأثير كبير. الشفافية مهمة هنا لشرح أثر أي إضافة على الوقت والتكلفة. مثلًا، متعدد المستخدمين خارج نطاقنا، يمكن التوصية بإصداره كمرحلة ثانية بعد الانتهاء من الحالي.

·       **محدودية الموارد البشرية**: المشروع ربما بيد مطوّر/مطوّرين فقط. أي مرض أو انشغال غير متوقع لأحدهم يؤثر مباشرة. _تخفيف_: وجود خطة شخصية لإدارة الوقت، وربما بناء المجتمع حول المشروع (لو مفتوح المصدر) لجذب مساهمات. وإذا وقع بالفعل غياب مطول لشخص أساسي، قد يُضطر لتقليل ميزات أو طلب مساعدة إضافية خارجية مؤقتة.

·       **تواصل وأهداف غير واضحة**: إن لم تتم مراجعة تقدم المشروع دوريا، قد نجد بنهاية الوقت أن بعض الأجزاء نفذت بغير المطلوب. _تخفيف_: اجتماعات مراجعة قصيرة (أسبوعية مثلاً) يعرض فيها المطور ما أنجزه (Demonstration) للمشرف أو العميل لضمان أن الاتجاه صحيح وتلقي التغذية الراجعة مبكرًا

### المخاطر التجارية/الاستخدام (Business & User Risks):

·       **عدم تقبل المستخدم للتطبيق**: هناك احتمال أن المستخدمين المستهدفين قد لا يجدون التطبيق مفيدًا كما توقعنا – ربما صعوبة استخدام، أو أنه لا يحفزهم كفاية للاستمرار. _تخفيف_: إشراك بعض المستخدمين (زملاء أو أفراد من الفئة المستهدفة) أثناء التطوير ليروا نسخ تجريبية ويعطوا رأيهم. يمكن إطلاق **نسخة تجريبية (Beta)** لفئة محدودة قبل الإصدار الرسمي للحصول على ملاحظات حقيقية. بناءً على الردود، نقوم بتحسينات. إن كان هناك مشكلة كبيرة في التبني (مثلاً واجهة العادات غير مفهومة)، الأفضل اكتشافها مبكرًا وتصحيحها.

·       **منافسة حلول أخرى**: لو فكرنا بالمشروع كمنتج، تطبيقات مثل Habitica وغيرها موجودة. قد يفضل البعض الاستمرار عليها. _تخفيف_: التركيز على إبراز _القيمة المضافة_ لتطبيقنا (الخصوصية، التكامل) في توجيه الاستخدام. أيضًا يمكن تحويله لميزة تشاركية بدلًا من منافسة: مثلا تمكين استيراد البيانات من تطبيقات أخرى لجذب المستخدمين (إذا وقت يسمح). ولكن بما أنه مشروع أكاديمي/شخصي في الأساس، ضغط المنافسة التجاري منخفض، الأهم إرضاء المستخدم الفردي الذي نجري الاختبار معه.

·       **مخاطر خارجية**: مثل تغير في منصة Windows أو تحديث يكسر التوافق. هذا أمر نادر أن يحدث فجأة في إطار 2025، لكن وارد مثلاً تحديث أمني يمنع بعض سلوكيات WPF. _تخفيف_: البقاء على اطلاع عبر مجتمع المطورين، واختبار التطبيق على آخر تحديثات Windows قبل التسليم. أيضًا التحسب لسياسات المستقبل: مثلاً لو Windows 11 أصبح يتطلب حزم تطبيق بشكل MSIX، ربما نجاري ذلك لضمان استمرارية التطبيق.

### مخاطر تتعلق بالبيانات:

·       **فقدان بيانات المستخدم**: خطأ برمجي ربما يمسح قاعدة البيانات أو يتلفها. سيكون ذلك كارثيًا على تجربة المستخدم. _تخفيف_: إجراء اختبارات مكثفة على عمليات الحذف وإعادة الضبط للتأكد من عدم حدوث مسح عارض. توفير آلية نسخ احتياطي كما ذكرنا كي يستطيع المستخدم استعادة بياناته لو حصل خلل. أثناء التطوير، دائمًا نحتفظ بنسخة من قاعدة بيانات الاختبار لتجنب خسارة العمل عند تجربة ميزات جديدة.

·       **أخطاء حسابية في الإحصائيات**: إن حسب التطبيق نسبًا أو رسومًا خاطئة، قد يتخذ المستخدم قرارات خاطئة أو يفقد الثقة. _تخفيف_: اختبار نتائج الإحصائيات يدويًا مقابل سيناريوهات معروفة (مثلا إدخال بيانات معروفة النسبة للتأكد من الرسم). استخدام مكتبات قياسية للحساب مثل LINQ في C# بدل حسابات مخصصة غير موثوقة. ولو ظهر الخطأ بعد الإطلاق، نطلق تحديثًا تصحيحيًا عاجل مع شرح للمستخدم.

### تصنيف واستراتيجية عامة:

يمكن تصنيف المخاطر كما ذكرت المصادر إلى فئات أربع: - المخاطر الفنية: تناولناها (جهل تقني، قاعدة البيانات...). - المخاطر في المشروع: تناولنا التخطيط والتواصل. - المخاطر الخارجية: يمكن أن تشمل أيضًا تغيرات بيئة أو دعم (مثلاً ظهور نسخة SQLite جديدة بتغييرات) – احتمال ضعيف، لكن التأقلم معها لو حدث. - المخاطر التجارية: لدينا نوعًا ما خطر تلبية توقعات العملاء والمستخدمين كما ذكر أعلاه.

إستراتيجيات التعامل كانت: - **المنع/التجنب**: كتعلم التقنيات، تقسيم العمل جيدًا، عدم إدخال تغييرات كبرى في آخر لحظة. - **التخفيف**: كالاختبارات، التواصل المستمر، بناء نسخ تجريبية. - **خطة الطوارئ**: مثلاً لو تعثر التقويم، لدينا خطة بديلة لعرض المهام في قائمة بحسب التاريخ (ليس أنيقًا لكنه ينجز المطلوب). أو لو انهار خيار التشفير، نعتمد على تأمين OS فقط ونوضح ذلك. - **القبول**: هناك مخاطر ضئيلة قد نقبلها كما هي (مثلاً احتمال ضئيل لتلف ملف SQLite إذا انقطع الكهرباء أثناء الكتابة، هذا أمر نقبله مع توعية المستخدم بأخذ نسخة من حين لآخر).

وعي الفريق بأن **المخاطر جزء طبيعي** من المشروع أمر مهم حتى لا نتفاجأ أو نتصرف برد فعل متأخر. لذلك قمنا بهذا التحليل الاستباقي. مع المراقبة والتقييم المستمر خلال التنفيذ، يمكننا تعديل إجراءاتنا وتقنياتنا للحد من تحقق هذه المخاطر أو تأثيرها. الهدف النهائي أن نصل للإطلاق دون مشاكل كبيرة غير محسوبة، وبالتالي مشروع ناجح بجودة عالية ومستقر

## 18. النتائج المتوقعة

من خلال إطلاق واستخدام هذا البرنامج، نتوقع تحقيق عدد من **النتائج والفوائد** للمستخدمين على الصعيدين العملي (الإنتاجية وتنظيم الوقت) والنفسي (الشعور بالإنجاز وتقليل التوتر). تستند هذه التوقعات إلى كل من منطق تصميم التطبيق ودراسات في مجال تطوير العادات والإنتاجية الشخصية.

### تحسين الإنتاجية وإدارة الوقت:

عند اعتماد المستخدم على التطبيق لتخطيط يومه وأسبوعه وتنظيم مهامه في التقويم، سيكون لديه رؤية أوضح لأولوياته ومهامه القادمة. هذا غالبًا سيؤدي إلى **تقليل النسيان والتخبط** في أداء المهام. سيتعود المستخدم على _تفريغ_ المهام من دماغه إلى التطبيق، مما يحرره من عبء التذكر المستمر ويتيح له تركيز طاقته على التنفيذ الفعلي. نتيجة ذلك نتوقع **زيادة في عدد المهام المنجزة** يوميًا وأسبوعيًا مقارنة بما قبل استخدام الأداة. أيضًا توزيع المهام على التقويم يساعد في منع التراكم في يوم واحد، مما يعني مواعيد تسليم أكثر انتظاماً.

### بناء عادات إيجابية واستدامتها:

قسم العادات مصمم لدعم مفهوم "قليل مستمر خير من كثير منقطع". مع التذكيرات اليومية والتسجيل، سيتمكن المستخدم من **خلق روتين يومي ثابت** بمرور الوقت. على سبيل المثال، إذا كان هدف المستخدم قراءة 20 صفحة يوميًا، التطبيق يذكّره، ويوثّق تقدمه ويكافئه معنوياً (برؤية سلسلة الإنجاز). بحلول أسابيع قليلة، قد تتحول هذه المهمة إلى عادة طبيعية. الدراسات تشير إلى أن تتبع السلوك ذاتيًا يزيد من احتمالية الالتزام، لذا نتوقع ارتفاع معدلات التزام المستخدمين بأهدافهم (مثل التمرين أو التعلم المستمر). كما وجدت إحدى الدراسات أن الطلاب الذين تتبعوا عادات دراستهم شعروا **بمشتتات أقل وتحسن مزاجهم أثناء الدراسة**، وهذا بالضبط ما نطمح له: أن يشعر المستخدم _بالإنجاز اليومي_ حتى في الأمور الصغيرة. النجاح في بناء عادة أو اثنتين قد يدفعه لتوسيع الأمر وتشجيع نفسه على عادات أخرى، مولدًا حلقة نجاح إيجابية.

### زيادة الوعي الذاتي وتحسين التخطيط:

من خلال قسم الإحصائيات، سيكتسب المستخدم **رؤية واضحة لنمط سلوكه**. مثلاً سيرى أنه غالبًا لا ينجز الكثير من المهام مساء الجمعة، أو أن عادة ما تسقط منه في منتصف الشهر عادة. هذا الوعي يسمح له بضبط خططه: ربما يقرر جعل الجمعة راحة بدل الشعور بالذنب، أو ينتبه لتقوية التزامه في فترة معينة من الشهر. البيانات تجعل الأمور "ملموسة" بدل مشاعر عابرة. كما أن رؤية التحسن على مدى أسابيع (مثلاً تصاعد الرسم البياني للمهام المنجزة) يعطي **دافعًا قويًا للاستمرار**. علميًا، يدعم علم النفس فكرة أن رؤية التقدم (progress) تعزز الدافعية لأنها تمنح إحساس الإنجاز. كذلك الاحتفاء بالإنجازات الصغيرة (micro-wins) مثل سلسلة عادة لأسبوع يعزز الحالة النفسية. لذا نتوقع أن مستخدمي التطبيق سيتمتعون **بمستويات أعلى من التحفيز والمعنويات** فيما يخص أهدافهم اليومية مقارنة بمن لا يوثقون أي شيء.

### تقليل التوتر والضغط النفسي:

الفوضى في المهام والعادات كثيرًا ما تؤدي إلى الشعور بالضغط و_الإرهاق الذهني_. عند استخدام التطبيق لتنظيم كل شيء، يخرج الكثير من الغموض. المستخدم يعرف ما الذي عليه فعله ومتى، ويرى أنه تدريجيًا يقوم بما عليه. هذا من شأنه تخفيف القلق. أيضًا وجود خطة ومكان لتتبعها يعطي شعورًا **بالسيطرة** على الوقت بدل الشعور بأن المهام تداهمه عشوائيًا. إضافة لذلك، لو التزم المستخدم بفترات راحة مبرمجة (يمكنه إضافة وقت راحة للتقويم)، سيجد توازنًا أفضل. نتوقع بالتالي انخفاض في ظاهرة _التسويف_ لأن التطبيق يبني لدى المستخدم عادة العمل وفق جدول. في جانب العادات، عندما ينجح المستخدم بتبني عادات صحية (نوم كافٍ، رياضة، تأمل) فإن لذلك أثر مباشر على **تحسين المزاج والصحة النفسية** كما تدعم ذلك دراسات الرفاهية. وعلى العكس، إذا كان هناك عادة سيئة يحاول التخلص منها، تتبعها قد يقللها.

### نتائج كمية متوقعة:

بالطبع يصعب إعطاء أرقام دقيقة دون تجربة واقعية، ولكن لو افترضنا مستخدمًا متوسط الالتزام: - قد يزيد إنتاجيته (المهام المنجزة) بنحو 10-20% بعد شهرين من استخدام النظام، نتيجة تحسين التنظيم. - قد يحقق استمرارية في عادة جديدة لمدة تتجاوز 30 يومًا متتاليًا، وهو إنجاز يضعه على مسار العادة طويلة الأمد. - ربما ينخفض معدل تأخره عن المواعيد النهائية من معدل X إلى X- (تحسن واضح) بسبب التخطيط المسبق. - إذا أجرينا استبيان رضا، نتوقع أن غالبية المستخدمين سيشعرون بالرضا ويقيمون التطبيق عاليًا في جانب _سهولة الاستخدام_ والإفادة. أما جانب _المتعة_ (Fun) فقد ينقسم حسب تفضيل الشخص (على عكس Habitica الأكثر متعة ربما ولكن أقل جدية).

### مخرجات تعليمية وشخصية (للمطور/الفريق):

لا ننسى أن المشروع نفسه تجربة تعلم. فمن **النتائج** أيضًا اكتساب المطور/الفريق خبرة في بناء تطبيق متكامل باستخدام تقنيات حديثة، ومعرفة أعمق بمجال **تحليل النظم** وإعداد وثائق SRS. هذه التجربة العملية تصقل المهارات التقنية (C#, WPF, Patterns) وأيضًا مهارات الإدارة (تقدير الوقت، التعامل مع تغييرات...).

### ملاحظات حول قياس النتائج:

يمكن بعد فترة من إطلاق البرنامج إجراء تقييم لقياس مدى تحقق النتائج: - استبيانات للمستخدمين حول تغير عاداتهم وإنتاجيتهم منذ استخدامه. - تحليل البيانات المجمعة (مجهولة الهوية) إن سمح المستخدم، مثل عدد المهام التي يضيفونها وينجزونها أسبوعيًا بمرور الوقت. - مقارنة مع **خط الأساس** (baseline) قبل استخدام التطبيق (ربما المستخدم نفسه يلاحظ الفرق أو نسأله كم مهمة كان ينجز سابقًا).

كما نتوقع **ملاحظات بنّاءة** من المستخدمين: قد يقترحون ميزات جديدة أو تحسينات، مما يدل على تفاعلهم واستفادتهم لدرجة أنهم يرغبون بالمزيد (وهذا مؤشر نجاح مهم).

باختصار، النتائج المتوقعة إيجابية تشمل رفع كفاءة إدارة الوقت وتحقيق الأهداف، وتحسين الشعور بالإنجاز وتقليل التوتر. هذا يمثّل **القيمة المضافة** الحقيقية للمشروع: تحويل مبادئ تنظيم الوقت وتطوير العادات إلى تطبيق فعّال يغير حياة المستخدم نحو الأفضل ولو بشكل تدريجي، مع إعطائه أدوات موضوعية لقياس هذا التحسن والاحتفاء به. وإن تحقق ذلك، يكون المشروع قد نجح في هدفه الأسمى كمساعد رقمي شخصي.

## 19. الخاتمة والتوصيات المستقبلية

في ختام هذا التقرير، قمنا باستعراض شامل لمشروع **برنامج إدارة المهام والحياة الشخصية** من مرحلة الفكرة إلى التصميم المفصل. تم تحليل المشكلة التي يحلها البرنامج، وأهدافه في تمكين المستخدمين من تحسين إنتاجيتهم وتطوير عادات إيجابية. تطرّقنا إلى المتطلبات الوظيفية وغير الوظيفية التي تحدد سلوك وجودة النظام، وتصميمنا المعماري الذي يستند إلى تقنيات حديثة (C# WPF وSQLite مع نمط MVVM) لضمان الكفاءة وقابلية الصيانة. كما قدّمنا تصورًا دقيقًا لواجهات المستخدم وتفاعلها، مع إبراز عناصر UX التي تجعل التجربة سهلة وممتعة. لم نغفل جانب الأمان لضمان موثوقية التطبيق وخصوصية بيانات المستخدم، ووثقنا هيكل قاعدة البيانات ومخططات UML للتأكد من تكامل العناصر مع بعضها. ومن خلال خطة التنفيذ الموضوعة، بيّنا كيف سنحول هذا التحليل إلى منتج فعلي خطوة بخطوة، مع إدراك للمخاطر المحتملة ووضع حلول استباقية لها

**مدى تحقيق الأهداف**: نتوقع أن التطبيق عند إكماله سيوفر للمستخدم منصة متكاملة لإدارة جميع جوانب نشاطاته اليومية في مكان واحد. وهذا التكامل هو نقطة القوة التي ستيسر عليه ما كان ينجزه عبر عدة أدوات منفصلة. بالتالي، سيكون المستخدم قادرًا على التركيز على إنجاز مهامه وتحسين نفسه بدلاً من الانشغال بالتنظيم بحد ذاته – أي أن التطبيق حقق غايته عندما يصبح عادة استخدامه جزءًا من روتين المستخدم لتنظيم حياته.

### التوصيات والتطويرات المستقبلية:

مشروع كهذا بطبيعته قابل للتطوير المستمر والإضافة مع تغير حاجات المستخدمين وتطور التقنيات. فيما يلي بعض **الاقتراحات المستقبلية** التي يمكن دراستها بعد الإصدار الأولي:

1.     **إطلاق نسخة للأجهزة المحمولة**: كثير من المستخدمين يرغبون بالوصول لجدولهم وعاداتهم عبر هواتفهم الذكية. تطوير تطبيق مرافق على Android/iOS سيكون خطوة مهمة. يمكن استعمال تقنيات مثل Xamarin أو MAUI التي تسمح بإعادة استخدام جزء من كود #C، أو حتى التحول إلى تطبيق ويب تقدمي (PWA). سيتطلب ذلك أيضًا توفير **مزامنة سحابية** لضمان أن بيانات سطح المكتب والهاتف محدثة باستمرار. ربما عبر خدمة بسيطة (حتى Google Drive أو Dropbox API) أو خادم خاص.

2.     **تكامل الذكاء الاصطناعي**: إدخال تقنيات الذكاء الاصطناعي يمكن أن يرتقي بتجربة التطبيق بشكل كبير. على سبيل المثال:

3.     _مساعد ذكي للتخطيط_: يقوم AI بتحليل جدول المستخدم وعاداته، ثم يقترح تخصيص أوقات للمهام بشكل مثالي (مثلاً "يبدو أنك أكثر إنتاجية صباحًا، اقترح وضع مهمة القراءة في الثامنة صباحًا").

4.     _تحليل المشاعر والملل_: إذا لاحظ الذكاء من خلال تفاعلات المستخدم (أو إدخالات يدوية) أنه يشعر بإحباط تجاه عادة ما، يمكن أن يقترح تقنيات لتحسين الالتزام أو يذكره بسبب اختياره لهذه العادة وتحقيق الأهداف بعيدة المدى.

5.     _المساعد الحواري_: يمكن دمج chatbot يستطيع الإجابة على استفسارات المستخدم مثل "كيف يمكنني زيادة إنتاجيتي غدًا؟" أو "اقترح علي جدولاً لأستعد لامتحاناتي القادمة"، بناءً على بياناته أو معلومات عامة بالطبع، تطبيق مثل هذا يجب أن يتم بحذر (خصوصية البيانات) وربما تدريجيًا كتحديث تجريبي.

6.     **دعم التعاون أو المشاركة**: حاليًا التطبيق شخصي بالكامل، ولكن قد يكون من المفيد مستقبلاً إضافة خيار لمشاركة أجزاء مع الآخرين. مثلاً مشاركة **التقويم** مع شريك حياتك أو زميل العمل لتنسيق المواعيد، أو مشاركة **التقدم في عادة** مع صديق لتشجيع المنافسة الودية. هذا يتطلب تحويلات في التصميم (وجود حسابات متعددة وحقوق مشاركة) لكنه يفتح مجالًا لاستخدامات اجتماعية.

7.     **تحسينات واجهة وميزات إضافية**:

8.     إضافة **لوحة داشبورد أكثر ديناميكية** في الصفحة الرئيسية تعرض لمحات من كل قسم (كود ملون يبرز ما يحتاج انتباهك فورًا اليوم).

9.     دعم **الإدخال الصوتي** أو المساعدات الصوتية: مثلاً قول "أضف اجتماع غدًا 5 مساء" ليُضاف تلقائيًا.

10.  **التكامل مع البريد الإلكتروني**: ربط المهام باستقبال رسائل بريد (إذا وردت رسالة فيها موعد، يقترح إضافتها للتقويم).

11.  **مستويات متقدمة من التخصيص**: كالقدرة على إنشاء فئات مخصصة للمهام أو العادات (Labels) ثم ترشيح الإحصائيات بناءً عليها.

12.  **ويدجت سطح مكتب**: يعرض التقويم اليومي أو قائمة المهام على شاشة الحاسوب بدون فتح التطبيق، لمتابعة سريعة.

13.  **تحسينات الأمان**: مع الوقت، قد نرغب بإضافة طبقة حماية إضافية مثل **قفل التطبيق ببصمة الإصبع أو التعرف على الوجه** (إذا كانت البيئة تدعم Windows Hello) لتعزيز أمان الوصول خاصة في حال وجود بيانات حساسة. أيضًا يمكن تشفير قاعدة البيانات بالكامل بكلمة مرور مستقلة عن كلمة الدخول.

14.  **تحسين الأداء لبيانات كبيرة**: إن توسع الاستخدام لسنوات، سنحتاج لضمان أن التطبيق لا يصبح بطيئًا. ممكن إضافة آليات أرشفة تلقائية (مثلاً أرشفة المهام والعادات القديمة جدًا في ملف آخر)، أو تحسين استعلامات الإحصاء (مثلاً حفظ بيانات مجمعة Snapshots بدل حساب كل مرة من الصفر).

15.  **توطين ودعم لغات أخرى**: حالما يستقر التطبيق، يمكن التفكير بترجمته للغات إضافية لتوسيع قاعدة المستخدمين الذين قد يستفيدون منه. الهندسة الحالية يمكن أن تستخدم موارد منفصلة للنصوص (Localization) لتسهيل الترجمة.

ختامًا، لقد وضع هذا التقرير الأساس **لمشروع طموح** يجمع بين عناصر عدة لخدمة المستخدم الفرد. التنفيذ الناجح سيثبت جدوى الدمج بين تتبع المهام وتطوير العادات ضمن أداة واحدة. ومع التوصيات المذكورة، توجد فرصة لنمو المشروع وتحسينه باستمرار ليلبي احتياجات المستخدمين في المستقبل، سواء عبر منصات جديدة أو ميزات ذكية مستحدثة. يبقى التركيز دائمًا على الهدف الجوهري: **مساعدة الناس على تنظيم حياتهم وتحقيق أهدافهم بشكل فعّال ومستدام** باستخدام التقنية. نأمل أن يكون هذا المشروع خطوة في هذا الاتجاه، وأن نرى ثماره الإيجابية تنعكس على مستخدميه كما خططنا.

دمج التقنية مع علوم الإنتاجية وتطوير الذات مجال واعد، ومشروعنا هذا مساهمة نرجو أن تكون مميزة وملهمة فيه.